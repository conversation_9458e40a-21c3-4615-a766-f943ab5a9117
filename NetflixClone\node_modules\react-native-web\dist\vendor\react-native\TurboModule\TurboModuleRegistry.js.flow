/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import type { TurboModule } from './RCTExport';
import invariant from 'fbjs/lib/invariant';
declare export function get<T: TurboModule>(name: string): ?T;
declare export function getEnforcing<T: TurboModule>(name: string): T;