{"version": 3, "file": "useOnAction.d.ts", "sourceRoot": "", "sources": ["../../../src/useOnAction.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,MAAM,EACN,mBAAmB,EACpB,MAAM,2BAA2B,CAAC;AAInC,OAAO,EACL,KAAK,mBAAmB,EACxB,KAAK,yBAAyB,EAE/B,MAAM,4BAA4B,CAAC;AACpC,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAC5C,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAGhE,KAAK,OAAO,GAAG;IACb,MAAM,EAAE,MAAM,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;IAClD,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,eAAe,CAAC;IAChC,QAAQ,EAAE,CAAC,KAAK,EAAE,eAAe,GAAG,YAAY,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC;IAC3E,eAAe,EAAE,mBAAmB,EAAE,CAAC;IACvC,qBAAqB,EAAE,MAAM,CAAC,MAAM,EAAE,yBAAyB,GAAG,SAAS,CAAC,CAAC;IAC7E,mBAAmB,EAAE,mBAAmB,CAAC;IACzC,OAAO,EAAE,sBAAsB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;CACpD,CAAC;AAEF;;;;;;;;GAQG;AACH,wBAAgB,WAAW,CAAC,EAC1B,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,GAAG,EACH,eAAe,EACf,qBAAqB,EACrB,mBAAmB,EACnB,OAAO,GACR,EAAE,OAAO,YAoBI,gBAAgB,sBACL,GAAG,CAAC,MAAM,CAAC,aA8GnC"}