{"version": 3, "names": ["React", "useImperativeHandle", "useRef", "NativeViewGestureHandler", "nativeViewProps", "jsx", "_jsx", "NATIVE_WRAPPER_PROPS_FILTER", "createNativeWrapper", "Component", "config", "ComponentWrapper", "forwardRef", "props", "ref", "gestureHandlerProps", "childProps", "Object", "keys", "reduce", "res", "key", "<PERSON><PERSON><PERSON><PERSON>", "includes", "enabled", "hitSlop", "testID", "_ref", "_gestureHandlerRef", "node", "current", "handlerTag", "children", "displayName", "render", "name"], "sourceRoot": "../../../src", "sources": ["handlers/createNativeWrapper.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AAEnD,SACEC,wBAAwB,EAExBC,eAAe,QACV,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAAAC,GAAA,IAAAC,IAAA;AAOA,MAAMC,2BAA2B,GAAG,CAClC,GAAGH,eAAe,EAClB,uBAAuB,EACvB,6BAA6B,CACrB;AAEV,eAAe,SAASI,mBAAmBA,CACzCC,SAAiC,EACjCC,MAA+C,GAAG,CAAC,CAAC,EACpD;EACA,MAAMC,gBAAgB,gBAAGX,KAAK,CAACY,UAAU,CAGvC,CAACC,KAAK,EAAEC,GAAG,KAAK;IAChB;IACA,MAAM;MAAEC,mBAAmB;MAAEC;IAAW,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC,CAACM,MAAM,CACnE,CAACC,GAAG,EAAEC,GAAG,KAAK;MACZ;MACA,MAAMC,WAA8B,GAAGf,2BAA2B;MAClE,IAAIe,WAAW,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;QAC7B;QACAD,GAAG,CAACL,mBAAmB,CAACM,GAAG,CAAC,GAAGR,KAAK,CAACQ,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL;QACAD,GAAG,CAACJ,UAAU,CAACK,GAAG,CAAC,GAAGR,KAAK,CAACQ,GAAG,CAAC;MAClC;MACA,OAAOD,GAAG;IACZ,CAAC,EACD;MACEL,mBAAmB,EAAE;QAAE,GAAGL;MAAO,CAAC;MAAE;MACpCM,UAAU,EAAE;QACVQ,OAAO,EAAEX,KAAK,CAACW,OAAO;QACtBC,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBC,MAAM,EAAEb,KAAK,CAACa;MAChB;IACF,CACF,CAAC;IACD,MAAMC,IAAI,GAAGzB,MAAM,CAAyB,IAAI,CAAC;IACjD,MAAM0B,kBAAkB,GAAG1B,MAAM,CAAyB,IAAI,CAAC;IAC/DD,mBAAmB,CACjBa,GAAG;IACH;IACA,MAAM;MACJ,MAAMe,IAAI,GAAGD,kBAAkB,CAACE,OAAO;MACvC;MACA,IAAIH,IAAI,CAACG,OAAO,IAAID,IAAI,EAAE;QACxB;QACAF,IAAI,CAACG,OAAO,CAACC,UAAU,GAAGF,IAAI,CAACE,UAAU;QACzC,OAAOJ,IAAI,CAACG,OAAO;MACrB;MACA,OAAO,IAAI;IACb,CAAC,EACD,CAACH,IAAI,EAAEC,kBAAkB,CAC3B,CAAC;IACD,oBACEtB,IAAA,CAACH,wBAAwB;MAAA,GACnBY,mBAAmB;MACvB;MACAD,GAAG,EAAEc,kBAAmB;MAAAI,QAAA,eACxB1B,IAAA,CAACG,SAAS;QAAA,GAAKO,UAAU;QAAEF,GAAG,EAAEa;MAAK,CAAE;IAAC,CAChB,CAAC;EAE/B,CAAC,CAAC;;EAEF;EACAhB,gBAAgB,CAACsB,WAAW,GAC1BxB,SAAS,EAAEwB,WAAW;EACtB;EACAxB,SAAS,EAAEyB,MAAM,EAAEC,IAAI,IACtB,OAAO1B,SAAS,KAAK,QAAQ,IAAIA,SAAU,IAC5C,kBAAkB;EAEpB,OAAOE,gBAAgB;AACzB", "ignoreList": []}