{"version": 3, "names": ["gestures", "<PERSON><PERSON><PERSON><PERSON>", "tag", "Error", "createGestureHandler", "handlerTag", "handler", "dropGestureHandler", "destroy", "getNodes"], "sourceRoot": "../../../src", "sources": ["web_hammer/NodeManager.ts"], "mappings": ";;AAAA,OAAO,MAAMA,QAA6B,GAAG,CAAC,CAAC;AAE/C,OAAO,SAASC,UAAUA,CAACC,GAAW,EAAE;EACtC,IAAIA,GAAG,IAAIF,QAAQ,EAAE;IACnB,OAAOA,QAAQ,CAACE,GAAG,CAAC;EACtB;EAEA,MAAM,IAAIC,KAAK,CAAC,sBAAsBD,GAAG,EAAE,CAAC;AAC9C;AAEA,OAAO,SAASE,oBAAoBA,CAACC,UAAkB,EAAEC,OAAY,EAAE;EACrE,IAAID,UAAU,IAAIL,QAAQ,EAAE;IAC1B,MAAM,IAAIG,KAAK,CAAC,oBAAoBE,UAAU,iBAAiB,CAAC;EAClE;EACAL,QAAQ,CAACK,UAAU,CAAC,GAAGC,OAAO;EAC9B;EACAN,QAAQ,CAACK,UAAU,CAAC,CAACA,UAAU,GAAGA,UAAU;AAC9C;AAEA,OAAO,SAASE,kBAAkBA,CAACF,UAAkB,EAAE;EACrD;EACA;EACA,IAAI,EAAEA,UAAU,IAAIL,QAAQ,CAAC,EAAE;IAC7B;EACF;EACAC,UAAU,CAACI,UAAU,CAAC,CAACG,OAAO,CAAC,CAAC;EAChC;EACA,OAAOR,QAAQ,CAACK,UAAU,CAAC;AAC7B;AAEA,OAAO,SAASI,QAAQA,CAAA,EAAG;EACzB,OAAO;IAAE,GAAGT;EAAS,CAAC;AACxB", "ignoreList": []}