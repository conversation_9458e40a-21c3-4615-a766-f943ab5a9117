import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  ImageBackground,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { netflixStyles } from '../styles/netflix';
import { NETFLIX_COLORS } from '../utils/constants';
import tmdbApi from '../services/tmdbApi';
import MediaRow from '../components/MediaRow';
import storageService from '../services/storage';

const MediaDetailScreen = ({ route, navigation }) => {
  const { item: initialItem, mediaType } = route.params;
  const [item, setItem] = useState(initialItem);
  const [loading, setLoading] = useState(true);
  const [similarContent, setSimilarContent] = useState([]);
  const [watchProgress, setWatchProgress] = useState(0);

  useEffect(() => {
    loadDetailData();
    loadWatchProgress();
  }, []);

  const loadDetailData = async () => {
    try {
      setLoading(true);
      
      let detailData;
      if (mediaType === 'tv') {
        detailData = await tmdbApi.getTVDetails(initialItem.id);
      } else {
        detailData = await tmdbApi.getMovieDetails(initialItem.id);
      }

      setItem(detailData);
      setSimilarContent(detailData.similar?.results || detailData.recommendations?.results || []);
    } catch (error) {
      console.error('Error loading detail data:', error);
      Alert.alert('Error', 'Failed to load details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadWatchProgress = async () => {
    try {
      const progress = await storageService.getWatchProgress(initialItem.id, mediaType);
      setWatchProgress(progress);
    } catch (error) {
      console.error('Error loading watch progress:', error);
    }
  };

  const handlePlayPress = async () => {
    try {
      if (mediaType === 'tv') {
        // For TV shows, navigate to episodes screen
        navigation.navigate('TVEpisodes', { item: { ...item, media_type: mediaType } });
      } else {
        // For movies, start playback directly
        await storageService.addToWatchHistory({
          id: item.id,
          type: mediaType,
          title: item.title || item.name,
          poster: item.poster_path,
          backdrop: item.backdrop_path,
          progress: watchProgress,
        });

        navigation.navigate('Player', {
          item: { ...item, media_type: mediaType },
          startTime: watchProgress
        });
      }
    } catch (error) {
      console.error('Error starting playback:', error);
      Alert.alert('Error', 'Failed to start playback. Please try again.');
    }
  };

  const handleItemPress = (selectedItem) => {
    const selectedMediaType = selectedItem.media_type || (selectedItem.first_air_date ? 'tv' : 'movie');
    navigation.push('MediaDetail', { 
      item: { ...selectedItem, media_type: selectedMediaType },
      mediaType: selectedMediaType 
    });
  };

  const formatRuntime = (runtime) => {
    if (!runtime) return '';
    const hours = Math.floor(runtime / 60);
    const minutes = runtime % 60;
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).getFullYear();
  };

  const getGenres = () => {
    if (!item.genres) return '';
    return item.genres.map(genre => genre.name).join(', ');
  };

  if (loading) {
    return (
      <View style={netflixStyles.loadingContainer}>
        <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
        <Text style={netflixStyles.loadingText}>Loading details...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={netflixStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={NETFLIX_COLORS.black} />
      
      <ScrollView style={netflixStyles.container}>
        {/* Hero Section */}
        <View style={netflixStyles.heroBanner}>
          <ImageBackground
            source={{ uri: tmdbApi.getBackdropUrl(item.backdrop_path, 'w1280') }}
            style={netflixStyles.heroImage}
          >
            {/* Back Button */}
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: 50,
                left: 20,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderRadius: 20,
                width: 40,
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 1,
              }}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color={NETFLIX_COLORS.white} />
            </TouchableOpacity>

            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)', NETFLIX_COLORS.black]}
              style={netflixStyles.heroGradient}
            >
              <Text style={netflixStyles.heroTitle}>
                {item.title || item.name}
              </Text>
              
              {/* Meta Info */}
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
                <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14 }}>
                  {formatDate(item.release_date || item.first_air_date)}
                </Text>
                {item.runtime && (
                  <>
                    <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14, marginHorizontal: 10 }}>•</Text>
                    <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14 }}>
                      {formatRuntime(item.runtime)}
                    </Text>
                  </>
                )}
                {item.vote_average && (
                  <>
                    <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14, marginHorizontal: 10 }}>•</Text>
                    <Ionicons name="star" size={14} color="#FFD700" />
                    <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14, marginLeft: 4 }}>
                      {item.vote_average.toFixed(1)}
                    </Text>
                  </>
                )}
              </View>

              <Text style={netflixStyles.heroOverview} numberOfLines={4}>
                {item.overview}
              </Text>
              
              <View style={{ flexDirection: 'row', gap: 10 }}>
                <TouchableOpacity 
                  style={netflixStyles.playButton}
                  onPress={handlePlayPress}
                >
                  <Ionicons name="play" size={20} color={NETFLIX_COLORS.black} />
                  <Text style={netflixStyles.playButtonText}>
                    {watchProgress > 0 ? 'Resume' : 'Play'}
                  </Text>
                </TouchableOpacity>
              </View>

              {watchProgress > 0 && (
                <View style={{ marginTop: 10 }}>
                  <View style={[netflixStyles.progressContainer, { position: 'relative', height: 6 }]}>
                    <View 
                      style={[
                        netflixStyles.progressBar, 
                        { width: `${watchProgress * 100}%`, height: 6 }
                      ]} 
                    />
                  </View>
                  <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 12, marginTop: 5 }}>
                    {Math.round(watchProgress * 100)}% watched
                  </Text>
                </View>
              )}
            </LinearGradient>
          </ImageBackground>
        </View>

        {/* Details Section */}
        <View style={{ padding: 20 }}>
          {getGenres() && (
            <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14, marginBottom: 10 }}>
              Genres: {getGenres()}
            </Text>
          )}
          
          {item.overview && (
            <View style={{ marginBottom: 20 }}>
              <Text style={{ color: NETFLIX_COLORS.white, fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
                Overview
              </Text>
              <Text style={{ color: NETFLIX_COLORS.lightGray, fontSize: 14, lineHeight: 20 }}>
                {item.overview}
              </Text>
            </View>
          )}
        </View>

        {/* Similar Content */}
        {similarContent.length > 0 && (
          <MediaRow
            title="More Like This"
            data={similarContent}
            onItemPress={handleItemPress}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default MediaDetailScreen;
