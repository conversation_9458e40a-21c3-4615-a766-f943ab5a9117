{"version": 3, "names": ["_gesture", "require", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "forceChange", "force", "ForceTouchGesture", "ContinousBaseGesture", "config", "constructor", "handler<PERSON>ame", "minForce", "max<PERSON><PERSON>ce", "feedbackOnActivation", "value", "onChange", "callback", "handlers", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/forceTouchGesture.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAKA;AACA;AACA;;AAKA,SAASC,qBAAqBA,CAC5BC,OAAiE,EACjEC,QAAmE,EACnE;EACA,SAAS;;EACT,IAAIC,aAAkD;EACtD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,WAAW,EAAEJ,OAAO,CAACK;IACvB,CAAC;EACH,CAAC,MAAM;IACLH,aAAa,GAAG;MACdE,WAAW,EAAEJ,OAAO,CAACK,KAAK,GAAGJ,QAAQ,CAACI;IACxC,CAAC;EACH;EAEA,OAAO;IAAE,GAAGL,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;;AAEA;AACA;AACA;AACO,MAAMI,iBAAiB,SAASC,6BAAoB,CAGzD;EACOC,MAAM,GAAgD,CAAC,CAAC;EAE/DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,0BAA0B;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACEC,QAAQA,CAACN,KAAa,EAAE;IACtB,IAAI,CAACG,MAAM,CAACG,QAAQ,GAAGN,KAAK;IAC5B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEO,QAAQA,CAACP,KAAa,EAAE;IACtB,IAAI,CAACG,MAAM,CAACI,QAAQ,GAAGP,KAAK;IAC5B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEQ,oBAAoBA,CAACC,KAAc,EAAE;IACnC,IAAI,CAACN,MAAM,CAACK,oBAAoB,GAAGC,KAAK;IACxC,OAAO,IAAI;EACb;EAEAC,QAAQA,CACNC,QAOS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAAClB,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF;;AAEA;AACA;AACA;AAFAE,OAAA,CAAAZ,iBAAA,GAAAA,iBAAA", "ignoreList": []}