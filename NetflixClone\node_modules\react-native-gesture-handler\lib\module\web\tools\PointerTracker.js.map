{"version": 3, "names": ["VelocityTracker", "MAX_POINTERS", "PointerTracker", "velocityTracker", "_trackedPointers", "Map", "touchEventsIds", "cachedAbsoluteAverages", "x", "y", "cachedRelativeAverages", "constructor", "lastMovedPointerId", "NaN", "i", "set", "addToTracker", "event", "trackedPointers", "has", "pointerId", "newElement", "abosoluteCoords", "relativeCoords", "offsetX", "offsetY", "timestamp", "time", "velocityX", "velocityY", "mapTouchEventId", "getAbsoluteCoordsAverage", "getRelativeCoordsAverage", "removeFromTracker", "delete", "removeMappedTouchId", "track", "pointerData", "get", "add", "velocity", "id", "mappedId", "touchId", "isNaN", "getMappedTouchEventId", "touchEventId", "key", "value", "entries", "getVelocity", "getLastAbsoluteCoords", "getLastRelativeCoords", "coordsSum", "getAbsoluteCoordsSum", "avgX", "size", "avgY", "averages", "getRelativeCoordsSum", "ignoredPointer", "sum", "for<PERSON>ach", "resetTracker", "reset", "clear", "shareCommonPointers", "stPointers", "ndPointers", "some", "includes", "trackedPointersCount", "trackedPointersIDs", "keys", "_value", "push"], "sourceRoot": "../../../../src", "sources": ["web/tools/PointerTracker.ts"], "mappings": ";;AACA,OAAOA,eAAe,MAAM,mBAAmB;AAU/C,MAAMC,YAAY,GAAG,EAAE;AAEvB,eAAe,MAAMC,cAAc,CAAC;EAC1BC,eAAe,GAAG,IAAIH,eAAe,CAAC,CAAC;EAC9BI,gBAAgB,GAAgC,IAAIC,GAAG,CAGtE,CAAC;EAEKC,cAAc,GAAwB,IAAID,GAAG,CAAiB,CAAC;EAI/DE,sBAAsB,GAAU;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAC9CC,sBAAsB,GAAU;IAAEF,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAE/CE,WAAWA,CAAA,EAAG;IACnB,IAAI,CAACC,kBAAkB,GAAGC,GAAG;IAE7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,EAAE,EAAEa,CAAC,EAAE;MACrC,IAAI,CAACR,cAAc,CAACS,GAAG,CAACD,CAAC,EAAED,GAAG,CAAC;IACjC;EACF;EAEOG,YAAYA,CAACC,KAAmB,EAAQ;IAC7C,IAAI,IAAI,CAACC,eAAe,CAACC,GAAG,CAACF,KAAK,CAACG,SAAS,CAAC,EAAE;MAC7C;IACF;IAEA,IAAI,CAACR,kBAAkB,GAAGK,KAAK,CAACG,SAAS;IAEzC,MAAMC,UAA0B,GAAG;MACjCC,eAAe,EAAE;QAAEd,CAAC,EAAES,KAAK,CAACT,CAAC;QAAEC,CAAC,EAAEQ,KAAK,CAACR;MAAE,CAAC;MAC3Cc,cAAc,EAAE;QAAEf,CAAC,EAAES,KAAK,CAACO,OAAO;QAAEf,CAAC,EAAEQ,KAAK,CAACQ;MAAQ,CAAC;MACtDC,SAAS,EAAET,KAAK,CAACU,IAAI;MACrBC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE;IACb,CAAC;IAED,IAAI,CAACX,eAAe,CAACH,GAAG,CAACE,KAAK,CAACG,SAAS,EAAEC,UAAU,CAAC;IACrD,IAAI,CAACS,eAAe,CAACb,KAAK,CAACG,SAAS,CAAC;IAErC,IAAI,CAACb,sBAAsB,GAAG,IAAI,CAACwB,wBAAwB,CAAC,CAAC;IAC7D,IAAI,CAACrB,sBAAsB,GAAG,IAAI,CAACsB,wBAAwB,CAAC,CAAC;EAC/D;EAEOC,iBAAiBA,CAACb,SAAiB,EAAQ;IAChD,IAAI,CAACF,eAAe,CAACgB,MAAM,CAACd,SAAS,CAAC;IACtC,IAAI,CAACe,mBAAmB,CAACf,SAAS,CAAC;EACrC;EAEOgB,KAAKA,CAACnB,KAAmB,EAAQ;IACtC,MAAMoB,WAAW,GAAG,IAAI,CAACnB,eAAe,CAACoB,GAAG,CAACrB,KAAK,CAACG,SAAS,CAAC;IAE7D,IAAI,CAACiB,WAAW,EAAE;MAChB;IACF;IAEA,IAAI,CAACzB,kBAAkB,GAAGK,KAAK,CAACG,SAAS;IAEzC,IAAI,CAACjB,eAAe,CAACoC,GAAG,CAACtB,KAAK,CAAC;IAC/B,MAAM,CAACW,SAAS,EAAEC,SAAS,CAAC,GAAG,IAAI,CAAC1B,eAAe,CAACqC,QAAQ;IAE5DH,WAAW,CAACT,SAAS,GAAGA,SAAS;IACjCS,WAAW,CAACR,SAAS,GAAGA,SAAS;IAEjCQ,WAAW,CAACf,eAAe,GAAG;MAAEd,CAAC,EAAES,KAAK,CAACT,CAAC;MAAEC,CAAC,EAAEQ,KAAK,CAACR;IAAE,CAAC;IACxD4B,WAAW,CAACd,cAAc,GAAG;MAAEf,CAAC,EAAES,KAAK,CAACO,OAAO;MAAEf,CAAC,EAAEQ,KAAK,CAACQ;IAAQ,CAAC;IAEnE,IAAI,CAACP,eAAe,CAACH,GAAG,CAACE,KAAK,CAACG,SAAS,EAAEiB,WAAW,CAAC;IAEtD,IAAI,CAAC9B,sBAAsB,GAAG,IAAI,CAACwB,wBAAwB,CAAC,CAAC;IAC7D,IAAI,CAACrB,sBAAsB,GAAG,IAAI,CAACsB,wBAAwB,CAAC,CAAC;EAC/D;;EAEA;EACQF,eAAeA,CAACW,EAAU,EAAQ;IACxC,KAAK,MAAM,CAACC,QAAQ,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACrC,cAAc,EAAE;MACrD,IAAIsC,KAAK,CAACD,OAAO,CAAC,EAAE;QAClB,IAAI,CAACrC,cAAc,CAACS,GAAG,CAAC2B,QAAQ,EAAED,EAAE,CAAC;QACrC;MACF;IACF;EACF;EAEQN,mBAAmBA,CAACM,EAAU,EAAQ;IAC5C,MAAMC,QAAgB,GAAG,IAAI,CAACG,qBAAqB,CAACJ,EAAE,CAAC;IACvD,IAAI,CAACG,KAAK,CAACF,QAAQ,CAAC,EAAE;MACpB,IAAI,CAACpC,cAAc,CAACS,GAAG,CAAC2B,QAAQ,EAAE7B,GAAG,CAAC;IACxC;EACF;EAEOgC,qBAAqBA,CAACC,YAAoB,EAAU;IACzD,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAI,IAAI,CAAC1C,cAAc,CAAC2C,OAAO,CAAC,CAAC,EAAE;MACxD,IAAID,KAAK,KAAKF,YAAY,EAAE;QAC1B,OAAOC,GAAG;MACZ;IACF;IAEA,OAAOlC,GAAG;EACZ;EAEOqC,WAAWA,CAAC9B,SAAiB,EAAE;IACpC,MAAMiB,WAAW,GAAG,IAAI,CAACnB,eAAe,CAACoB,GAAG,CAAClB,SAAS,CAAC;IAEvD,OAAOiB,WAAW,GACd;MACE7B,CAAC,EAAE6B,WAAW,CAACT,SAAS;MACxBnB,CAAC,EAAE4B,WAAW,CAACR;IACjB,CAAC,GACD,IAAI;EACV;EAEOsB,qBAAqBA,CAAC/B,SAAkB,EAAE;IAC/C,OAAO,IAAI,CAACF,eAAe,CAACoB,GAAG,CAAClB,SAAS,IAAI,IAAI,CAACR,kBAAkB,CAAC,EACjEU,eAAe;EACrB;EAEO8B,qBAAqBA,CAAChC,SAAkB,EAAE;IAC/C,OAAO,IAAI,CAACF,eAAe,CAACoB,GAAG,CAAClB,SAAS,IAAI,IAAI,CAACR,kBAAkB,CAAC,EACjEW,cAAc;EACpB;;EAEA;EACA;EACA;EACA;;EAEOQ,wBAAwBA,CAAA,EAAG;IAChC,MAAMsB,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAE7C,MAAMC,IAAI,GAAGF,SAAS,CAAC7C,CAAC,GAAG,IAAI,CAACU,eAAe,CAACsC,IAAI;IACpD,MAAMC,IAAI,GAAGJ,SAAS,CAAC5C,CAAC,GAAG,IAAI,CAACS,eAAe,CAACsC,IAAI;IAEpD,MAAME,QAAQ,GAAG;MACflD,CAAC,EAAEoC,KAAK,CAACW,IAAI,CAAC,GAAG,IAAI,CAAChD,sBAAsB,CAACC,CAAC,GAAG+C,IAAI;MACrD9C,CAAC,EAAEmC,KAAK,CAACa,IAAI,CAAC,GAAG,IAAI,CAAClD,sBAAsB,CAACE,CAAC,GAAGgD;IACnD,CAAC;IAED,OAAOC,QAAQ;EACjB;EAEO1B,wBAAwBA,CAAA,EAAG;IAChC,MAAMqB,SAAS,GAAG,IAAI,CAACM,oBAAoB,CAAC,CAAC;IAE7C,MAAMJ,IAAI,GAAGF,SAAS,CAAC7C,CAAC,GAAG,IAAI,CAACU,eAAe,CAACsC,IAAI;IACpD,MAAMC,IAAI,GAAGJ,SAAS,CAAC5C,CAAC,GAAG,IAAI,CAACS,eAAe,CAACsC,IAAI;IAEpD,MAAME,QAAQ,GAAG;MACflD,CAAC,EAAEoC,KAAK,CAACW,IAAI,CAAC,GAAG,IAAI,CAAC7C,sBAAsB,CAACF,CAAC,GAAG+C,IAAI;MACrD9C,CAAC,EAAEmC,KAAK,CAACa,IAAI,CAAC,GAAG,IAAI,CAAC/C,sBAAsB,CAACD,CAAC,GAAGgD;IACnD,CAAC;IAED,OAAOC,QAAQ;EACjB;EAEOJ,oBAAoBA,CAACM,cAAuB,EAAE;IACnD,MAAMC,GAAG,GAAG;MAAErD,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAE1B,IAAI,CAACS,eAAe,CAAC4C,OAAO,CAAC,CAACd,KAAK,EAAED,GAAG,KAAK;MAC3C,IAAIA,GAAG,KAAKa,cAAc,EAAE;QAC1BC,GAAG,CAACrD,CAAC,IAAIwC,KAAK,CAAC1B,eAAe,CAACd,CAAC;QAChCqD,GAAG,CAACpD,CAAC,IAAIuC,KAAK,CAAC1B,eAAe,CAACb,CAAC;MAClC;IACF,CAAC,CAAC;IAEF,OAAOoD,GAAG;EACZ;EAEOF,oBAAoBA,CAACC,cAAuB,EAAE;IACnD,MAAMC,GAAG,GAAG;MAAErD,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAE1B,IAAI,CAACS,eAAe,CAAC4C,OAAO,CAAC,CAACd,KAAK,EAAED,GAAG,KAAK;MAC3C,IAAIA,GAAG,KAAKa,cAAc,EAAE;QAC1BC,GAAG,CAACrD,CAAC,IAAIwC,KAAK,CAACzB,cAAc,CAACf,CAAC;QAC/BqD,GAAG,CAACpD,CAAC,IAAIuC,KAAK,CAACzB,cAAc,CAACd,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAOoD,GAAG;EACZ;EAEOE,YAAYA,CAAA,EAAS;IAC1B,IAAI,CAAC5D,eAAe,CAAC6D,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC9C,eAAe,CAAC+C,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACrD,kBAAkB,GAAGC,GAAG;IAE7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,EAAE,EAAEa,CAAC,EAAE;MACrC,IAAI,CAACR,cAAc,CAACS,GAAG,CAACD,CAAC,EAAED,GAAG,CAAC;IACjC;EACF;EAEA,OAAcqD,mBAAmBA,CAC/BC,UAAoB,EACpBC,UAAoB,EACX;IACT,OAAOD,UAAU,CAACE,IAAI,CAAEjD,SAAS,IAAKgD,UAAU,CAACE,QAAQ,CAAClD,SAAS,CAAC,CAAC;EACvE;EAEA,IAAWmD,oBAAoBA,CAAA,EAAW;IACxC,OAAO,IAAI,CAACrD,eAAe,CAACsC,IAAI;EAClC;EAEA,IAAWgB,kBAAkBA,CAAA,EAAG;IAC9B,MAAMC,IAAc,GAAG,EAAE;IAEzB,IAAI,CAACvD,eAAe,CAAC4C,OAAO,CAAC,CAACY,MAAM,EAAE3B,GAAG,KAAK;MAC5C0B,IAAI,CAACE,IAAI,CAAC5B,GAAG,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO0B,IAAI;EACb;EAEA,IAAWvD,eAAeA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACd,gBAAgB;EAC9B;AACF", "ignoreList": []}