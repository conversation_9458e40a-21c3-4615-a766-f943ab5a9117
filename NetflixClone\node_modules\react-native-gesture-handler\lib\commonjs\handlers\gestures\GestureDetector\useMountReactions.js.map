{"version": 3, "names": ["_utils", "require", "_mountRegistry", "_react", "shouldUpdateDetector", "relation", "gesture", "undefined", "tag", "transformIntoHandlerTags", "handlerTag", "useMountReactions", "updateDetector", "state", "useEffect", "MountRegistry", "addMountListener", "attachedGesture", "attachedGestures", "blocksHandlers", "config", "requireToFail", "simultaneousWith"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useMountReactions.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAGA,SAASG,oBAAoBA,CAC3BC,QAAkC,EAClCC,OAA+B,EAC/B;EACA,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1B,OAAO,KAAK;EACd;EAEA,KAAK,MAAMC,GAAG,IAAI,IAAAC,+BAAwB,EAACJ,QAAQ,CAAC,EAAE;IACpD,IAAIG,GAAG,KAAKF,OAAO,CAACI,UAAU,EAAE;MAC9B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEO,SAASC,iBAAiBA,CAC/BC,cAA0B,EAC1BC,KAA2B,EAC3B;EACA,IAAAC,gBAAS,EAAC,MAAM;IACd,OAAOC,4BAAa,CAACC,gBAAgB,CAAEV,OAAO,IAAK;MACjD;MACA;MACA;MACA,KAAK,MAAMW,eAAe,IAAIJ,KAAK,CAACK,gBAAgB,EAAE;QACpD,MAAMC,cAAc,GAAGF,eAAe,CAACG,MAAM,CAACD,cAAc;QAC5D,MAAME,aAAa,GAAGJ,eAAe,CAACG,MAAM,CAACC,aAAa;QAC1D,MAAMC,gBAAgB,GAAGL,eAAe,CAACG,MAAM,CAACE,gBAAgB;QAEhE,IACElB,oBAAoB,CAACe,cAAc,EAAEb,OAAO,CAAC,IAC7CF,oBAAoB,CAACiB,aAAa,EAAEf,OAAO,CAAC,IAC5CF,oBAAoB,CAACkB,gBAAgB,EAAEhB,OAAO,CAAC,EAC/C;UACAM,cAAc,CAAC,CAAC;;UAEhB;UACA;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,cAAc,EAAEC,KAAK,CAAC,CAAC;AAC7B", "ignoreList": []}