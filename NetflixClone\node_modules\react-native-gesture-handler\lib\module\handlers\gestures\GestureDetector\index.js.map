{"version": 3, "names": ["React", "useContext", "useEffect", "useLayoutEffect", "useMemo", "useRef", "Platform", "findNodeHandle", "isTestEnv", "GestureHandlerRootViewContext", "useAnimatedGesture", "attachHandlers", "needsToReattach", "dropHandlers", "useWebEventHandlers", "Wrap", "AnimatedWrap", "useDetectorUpdater", "useViewRefHandler", "useMountReactions", "jsx", "_jsx", "propagateDetectorConfig", "props", "gesture", "keysToPropagate", "key", "value", "undefined", "g", "toGestureArray", "config", "GestureDetector", "rootViewContext", "__DEV__", "OS", "Error", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "shouldUseReanimated", "some", "webEventHandlersRef", "state", "firstRender", "viewRef", "previousViewTag", "forceRebuildReanimatedEvent", "current", "preparedGesture", "attachedGestures", "animatedEventHandler", "animatedHandlers", "isMounted", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "needsToRebuildReanimatedEvent", "viewTag", "ref", "onGestureHandlerEvent", "children"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/index.tsx"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IACVC,UAAU,EACVC,SAAS,EACTC,eAAe,EACfC,OAAO,EACPC,MAAM,QACD,OAAO;AACd,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,cAAc,MAAM,yBAAyB;AAIpD,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,OAAOC,6BAA6B,MAAM,wCAAwC;AAElF,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,SAAS;AAC7C,SAASC,IAAI,EAAEC,YAAY,QAAQ,QAAQ;AAC3C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAExD,SAASC,uBAAuBA,CAC9BC,KAA2B,EAC3BC,OAAsC,EACtC;EACA,MAAMC,eAA+C,GAAG,CACtD,YAAY,EACZ,mBAAmB,EACnB,aAAa,CACd;EAED,KAAK,MAAMC,GAAG,IAAID,eAAe,EAAE;IACjC,MAAME,KAAK,GAAGJ,KAAK,CAACG,GAAG,CAAC;IACxB,IAAIC,KAAK,KAAKC,SAAS,EAAE;MACvB;IACF;IAEA,KAAK,MAAMC,CAAC,IAAIL,OAAO,CAACM,cAAc,CAAC,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGF,CAAC,CAACE,MAAoC;MACrDA,MAAM,CAACL,GAAG,CAAC,GAAGC,KAAK;IACrB;EACF;AACF;AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,eAAe,GAAIT,KAA2B,IAAK;EAC9D,MAAMU,eAAe,GAAGhC,UAAU,CAACQ,6BAA6B,CAAC;EACjE,IAAIyB,OAAO,IAAI,CAACD,eAAe,IAAI,CAACzB,SAAS,CAAC,CAAC,IAAIF,QAAQ,CAAC6B,EAAE,KAAK,KAAK,EAAE;IACxE,MAAM,IAAIC,KAAK,CACb,wNACF,CAAC;EACH;;EAEA;EACA,MAAMC,aAAa,GAAGd,KAAK,CAACC,OAAO;EACnCF,uBAAuB,CAACC,KAAK,EAAEc,aAAa,CAAC;EAE7C,MAAMC,gBAAgB,GAAGlC,OAAO,CAC9B,MAAMiC,aAAa,CAACP,cAAc,CAAC,CAAC,EACpC,CAACO,aAAa,CAChB,CAAC;EACD,MAAME,mBAAmB,GAAGD,gBAAgB,CAACE,IAAI,CAC9CX,CAAC,IAAKA,CAAC,CAACU,mBACX,CAAC;EAED,MAAME,mBAAmB,GAAG3B,mBAAmB,CAAC,CAAC;EACjD;EACA,MAAM4B,KAAK,GAAGrC,MAAM,CAAuB;IACzCsC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,IAAI;IACbC,eAAe,EAAE,CAAC,CAAC;IACnBC,2BAA2B,EAAE;EAC/B,CAAC,CAAC,CAACC,OAAO;EAEV,MAAMC,eAAe,GAAGhD,KAAK,CAACK,MAAM,CAAuB;IACzD4C,gBAAgB,EAAE,EAAE;IACpBC,oBAAoB,EAAE,IAAI;IAC1BC,gBAAgB,EAAE,IAAI;IACtBZ,mBAAmB,EAAEA,mBAAmB;IACxCa,SAAS,EAAE;EACb,CAAC,CAAC,CAACL,OAAO;EAEV,MAAMM,sBAAsB,GAAGpC,kBAAkB,CAC/CyB,KAAK,EACLM,eAAe,EACfV,gBAAgB,EAChBD,aAAa,EACbI,mBACF,CAAC;EAED,MAAMa,UAAU,GAAGpC,iBAAiB,CAACwB,KAAK,EAAEW,sBAAsB,CAAC;;EAEnE;EACA;EACA,MAAME,6BAA6B,GACjCb,KAAK,CAACC,WAAW,IACjBD,KAAK,CAACI,2BAA2B,IACjClC,eAAe,CAACoC,eAAe,EAAEV,gBAAgB,CAAC;EACpDI,KAAK,CAACI,2BAA2B,GAAG,KAAK;EAEzCpC,kBAAkB,CAACsC,eAAe,EAAEO,6BAA6B,CAAC;EAElEpD,eAAe,CAAC,MAAM;IACpB,MAAMqD,OAAO,GAAGjD,cAAc,CAACmC,KAAK,CAACE,OAAO,CAAW;IACvDI,eAAe,CAACI,SAAS,GAAG,IAAI;IAEhCzC,cAAc,CAAC;MACbqC,eAAe;MACfX,aAAa;MACbC,gBAAgB;MAChBG,mBAAmB;MACnBe;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXR,eAAe,CAACI,SAAS,GAAG,KAAK;MACjCvC,YAAY,CAACmC,eAAe,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN9C,SAAS,CAAC,MAAM;IACd,IAAIwC,KAAK,CAACC,WAAW,EAAE;MACrBD,KAAK,CAACC,WAAW,GAAG,KAAK;IAC3B,CAAC,MAAM;MACLU,sBAAsB,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC9B,KAAK,CAAC,CAAC;EAEXJ,iBAAiB,CAACkC,sBAAsB,EAAEL,eAAe,CAAC;EAE1D,IAAIT,mBAAmB,EAAE;IACvB,oBACElB,IAAA,CAACL,YAAY;MACXyC,GAAG,EAAEH,UAAW;MAChBI,qBAAqB,EAAEV,eAAe,CAACE,oBAAqB;MAAAS,QAAA,EAC3DpC,KAAK,CAACoC;IAAQ,CACH,CAAC;EAEnB,CAAC,MAAM;IACL,oBAAOtC,IAAA,CAACN,IAAI;MAAC0C,GAAG,EAAEH,UAAW;MAAAK,QAAA,EAAEpC,KAAK,CAACoC;IAAQ,CAAO,CAAC;EACvD;AACF,CAAC", "ignoreList": []}