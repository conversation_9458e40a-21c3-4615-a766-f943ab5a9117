{"version": 3, "names": ["_createHandler", "_interopRequireDefault", "require", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "__esModule", "default", "pinchHandlerName", "exports", "PinchGestureHandler", "createHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/PinchGestureHandler.ts"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAGgC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhC;AACA;AACA;;AAIO,MAAMG,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAG,qBAAqB;;AAErD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACO,MAAME,mBAAmB,GAAAD,OAAA,CAAAC,mBAAA,GAAG,IAAAC,sBAAa,EAG9C;EACAC,IAAI,EAAEJ,gBAAgB;EACtBK,YAAY,EAAEC,6CAAuB;EACrCC,MAAM,EAAE,CAAC;AACX,CAAC,CAAC", "ignoreList": []}