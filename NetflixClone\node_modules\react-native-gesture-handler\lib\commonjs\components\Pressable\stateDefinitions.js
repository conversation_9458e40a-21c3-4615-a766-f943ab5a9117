"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StateMachineEvent = void 0;
exports.getConfiguredStateMachine = getConfiguredStateMachine;
var _reactNative = require("react-native");
var _StateMachine = require("./StateMachine");
let StateMachineEvent = exports.StateMachineEvent = /*#__PURE__*/function (StateMachineEvent) {
  StateMachineEvent["NATIVE_BEGIN"] = "nativeBegin";
  StateMachineEvent["NATIVE_START"] = "nativeStart";
  StateMachineEvent["FINALIZE"] = "finalize";
  StateMachineEvent["LONG_PRESS_TOUCHES_DOWN"] = "longPressTouchesDown";
  return StateMachineEvent;
}({});
function getAndroidStateMachine(handlePressIn, handlePressOut) {
  return new _StateMachine.PressableStateMachine([{
    eventName: StateMachineEvent.NATIVE_BEGIN
  }, {
    eventName: StateMachineEvent.LONG_PRESS_TOUCHES_DOWN,
    callback: handlePressIn
  }, {
    eventName: StateMachineEvent.FINALIZE,
    callback: handlePressOut
  }]);
}
function getIosStateMachine(handlePressIn, handlePressOut) {
  return new _StateMachine.PressableStateMachine([{
    eventName: StateMachineEvent.LONG_PRESS_TOUCHES_DOWN
  }, {
    eventName: StateMachineEvent.NATIVE_START,
    callback: handlePressIn
  }, {
    eventName: StateMachineEvent.FINALIZE,
    callback: handlePressOut
  }]);
}
function getWebStateMachine(handlePressIn, handlePressOut) {
  return new _StateMachine.PressableStateMachine([{
    eventName: StateMachineEvent.NATIVE_BEGIN
  }, {
    eventName: StateMachineEvent.NATIVE_START
  }, {
    eventName: StateMachineEvent.LONG_PRESS_TOUCHES_DOWN,
    callback: handlePressIn
  }, {
    eventName: StateMachineEvent.FINALIZE,
    callback: handlePressOut
  }]);
}
function getMacosStateMachine(handlePressIn, handlePressOut) {
  return new _StateMachine.PressableStateMachine([{
    eventName: StateMachineEvent.LONG_PRESS_TOUCHES_DOWN
  }, {
    eventName: StateMachineEvent.NATIVE_BEGIN,
    callback: handlePressIn
  }, {
    eventName: StateMachineEvent.NATIVE_START
  }, {
    eventName: StateMachineEvent.FINALIZE,
    callback: handlePressOut
  }]);
}
function getUniversalStateMachine(handlePressIn, handlePressOut) {
  return new _StateMachine.PressableStateMachine([{
    eventName: StateMachineEvent.FINALIZE,
    callback: event => {
      handlePressIn(event);
      handlePressOut(event);
    }
  }]);
}
function getConfiguredStateMachine(handlePressIn, handlePressOut) {
  if (_reactNative.Platform.OS === 'android') {
    return getAndroidStateMachine(handlePressIn, handlePressOut);
  } else if (_reactNative.Platform.OS === 'ios') {
    return getIosStateMachine(handlePressIn, handlePressOut);
  } else if (_reactNative.Platform.OS === 'web') {
    return getWebStateMachine(handlePressIn, handlePressOut);
  } else if (_reactNative.Platform.OS === 'macos') {
    return getMacosStateMachine(handlePressIn, handlePressOut);
  } else {
    // Unknown platform - using minimal universal setup.
    return getUniversalStateMachine(handlePressIn, handlePressOut);
  }
}
//# sourceMappingURL=stateDefinitions.js.map