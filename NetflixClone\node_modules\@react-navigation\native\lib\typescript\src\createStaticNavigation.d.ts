import { type NavigationContainerRef, type ParamListBase, type StaticNavigation } from '@react-navigation/core';
import * as React from 'react';
import { NavigationContainer } from './NavigationContainer';
import type { LinkingOptions } from './types';
type Props = Omit<React.ComponentProps<typeof NavigationContainer>, 'linking' | 'children'> & {
    /**
     * Options for deep linking.
     */
    linking?: Omit<LinkingOptions<ParamListBase>, 'config' | 'enabled'> & {
        /**
         * Whether deep link handling should be enabled.
         * Defaults to `true` if any `linking` options are specified, `false` otherwise.
         *
         * When 'auto' is specified, all leaf screens will get a autogenerated path.
         * The generated path will be a kebab-case version of the screen name.
         * This can be overridden for specific screens by specifying `linking` for the screen.
         */
        enabled?: 'auto' | true | false;
        /**
         * Additional configuration
         */
        config?: Omit<NonNullable<LinkingOptions<ParamListBase>['config']>, 'screens'>;
    };
};
/**
 * Create a navigation component from a static navigation config.
 * The returned component is a wrapper around `NavigationContainer`.
 *
 * @param tree Static navigation config.
 * @returns Navigation component to use in your app.
 */
export declare function createStaticNavigation(tree: StaticNavigation<any, any, any>): React.ForwardRefExoticComponent<Omit<Props, "ref"> & React.RefAttributes<NavigationContainerRef<ParamListBase>>>;
export {};
//# sourceMappingURL=createStaticNavigation.d.ts.map