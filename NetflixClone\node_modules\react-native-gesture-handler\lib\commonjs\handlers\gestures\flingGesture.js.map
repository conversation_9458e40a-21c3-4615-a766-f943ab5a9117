{"version": 3, "names": ["_gesture", "require", "FlingGesture", "BaseGesture", "config", "constructor", "handler<PERSON>ame", "numberOfPointers", "pointers", "direction", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/flingGesture.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAIO,MAAMC,YAAY,SAASC,oBAAW,CAAkC;EACtEC,MAAM,GAA2C,CAAC,CAAC;EAE1DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,qBAAqB;EAC1C;;EAEA;AACF;AACA;AACA;EACEC,gBAAgBA,CAACC,QAAgB,EAAE;IACjC,IAAI,CAACJ,MAAM,CAACG,gBAAgB,GAAGC,QAAQ;IACvC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,SAASA,CAACA,SAAiB,EAAE;IAC3B,IAAI,CAACL,MAAM,CAACK,SAAS,GAAGA,SAAS;IACjC,OAAO,IAAI;EACb;AACF;AAACC,OAAA,CAAAR,YAAA,GAAAA,YAAA", "ignoreList": []}