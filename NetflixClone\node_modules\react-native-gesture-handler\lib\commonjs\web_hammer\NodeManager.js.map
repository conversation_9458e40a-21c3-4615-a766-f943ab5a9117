{"version": 3, "names": ["gestures", "exports", "<PERSON><PERSON><PERSON><PERSON>", "tag", "Error", "createGestureHandler", "handlerTag", "handler", "dropGestureHandler", "destroy", "getNodes"], "sourceRoot": "../../../src", "sources": ["web_hammer/NodeManager.ts"], "mappings": ";;;;;;;;;;AAAO,MAAMA,QAA6B,GAAAC,OAAA,CAAAD,QAAA,GAAG,CAAC,CAAC;AAExC,SAASE,UAAUA,CAACC,GAAW,EAAE;EACtC,IAAIA,GAAG,IAAIH,QAAQ,EAAE;IACnB,OAAOA,QAAQ,CAACG,GAAG,CAAC;EACtB;EAEA,MAAM,IAAIC,KAAK,CAAC,sBAAsBD,GAAG,EAAE,CAAC;AAC9C;AAEO,SAASE,oBAAoBA,CAACC,UAAkB,EAAEC,OAAY,EAAE;EACrE,IAAID,UAAU,IAAIN,QAAQ,EAAE;IAC1B,MAAM,IAAII,KAAK,CAAC,oBAAoBE,UAAU,iBAAiB,CAAC;EAClE;EACAN,QAAQ,CAACM,UAAU,CAAC,GAAGC,OAAO;EAC9B;EACAP,QAAQ,CAACM,UAAU,CAAC,CAACA,UAAU,GAAGA,UAAU;AAC9C;AAEO,SAASE,kBAAkBA,CAACF,UAAkB,EAAE;EACrD;EACA;EACA,IAAI,EAAEA,UAAU,IAAIN,QAAQ,CAAC,EAAE;IAC7B;EACF;EACAE,UAAU,CAACI,UAAU,CAAC,CAACG,OAAO,CAAC,CAAC;EAChC;EACA,OAAOT,QAAQ,CAACM,UAAU,CAAC;AAC7B;AAEO,SAASI,QAAQA,CAAA,EAAG;EACzB,OAAO;IAAE,GAAGV;EAAS,CAAC;AACxB", "ignoreList": []}