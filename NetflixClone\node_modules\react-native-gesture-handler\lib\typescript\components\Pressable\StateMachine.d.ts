import { PressableEvent } from './PressableProps';
interface StateDefinition {
    eventName: string;
    callback?: (event: PressableEvent) => void;
}
declare class PressableStateMachine {
    private states;
    private currentStepIndex;
    private eventPayload;
    constructor(steps: StateDefinition[]);
    reset(): void;
    handleEvent(eventName: string, eventPayload?: PressableEvent): void;
}
export { PressableStateMachine };
