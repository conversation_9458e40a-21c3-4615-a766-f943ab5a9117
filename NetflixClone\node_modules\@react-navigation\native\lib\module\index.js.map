{"version": 3, "names": ["createStaticNavigation", "Link", "LinkingContext", "LocaleDirContext", "NavigationContainer", "ServerContainer", "DarkTheme", "DefaultTheme", "UnhandledLinkingContext", "UNSTABLE_UnhandledLinkingContext", "useLinkBuilder", "useLinkProps", "useLinkTo", "useLocale", "useScrollToTop"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;AAAA,SAASA,sBAAsB,QAAQ,6BAA0B;AACjE,SAASC,IAAI,QAAQ,WAAQ;AAC7B,SAASC,cAAc,QAAQ,qBAAkB;AACjD,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,mBAAmB,QAAQ,0BAAuB;AAC3D,SAASC,eAAe,QAAQ,sBAAmB;AACnD,SAASC,SAAS,QAAQ,wBAAqB;AAC/C,SAASC,YAAY,QAAQ,2BAAwB;AACrD,cAAc,YAAS;AACvB,SAASC,uBAAuB,IAAIC,gCAAgC,QAAQ,8BAA2B;AACvG,SAASC,cAAc,QAAQ,qBAAkB;AACjD,SAAyBC,YAAY,QAAQ,mBAAgB;AAC7D,SAASC,SAAS,QAAQ,gBAAa;AACvC,SAASC,SAAS,QAAQ,gBAAa;AACvC,SAASC,cAAc,QAAQ,qBAAkB;AACjD,cAAc,wBAAwB", "ignoreList": []}