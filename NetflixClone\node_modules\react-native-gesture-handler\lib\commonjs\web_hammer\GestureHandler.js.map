{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_reactNative", "_State", "_constants", "NodeManager", "_interopRequireWildcard", "_ghQueueMicrotask", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "gestureInstances", "Gesture<PERSON>andler", "isGestureRunning", "view", "hasGestureFailed", "hammer", "initialRotation", "config", "previousState", "State", "UNDETERMINED", "pendingGestures", "oldState", "lastSentState", "id", "name", "gestureInstance", "isNative", "isDiscrete", "shouldEnableGestureOnSetup", "Error", "constructor", "hasCustomActivationCriteria", "getConfig", "onWaitingEnded", "_gesture", "removePendingGesture", "addPendingGesture", "gesture", "isGestureEnabledForEvent", "_config", "_recognizer", "_event", "success", "NativeGestureClass", "updateHasCustomActivationCriteria", "clearSelfAsPending", "Array", "isArray", "waitFor", "updateGestureConfig", "enabled", "props", "ensureConfig", "sync", "destroy", "stop", "isPointInView", "x", "y", "rect", "getBoundingClientRect", "pointerInside", "left", "right", "top", "bottom", "getState", "type", "EventMap", "transformEventData", "event", "eventType", "maxPointers", "numberOfPointers", "changedTouch", "changedPointers", "clientX", "clientY", "state", "nativeEvent", "transformNativeEvent", "handlerTag", "target", "ref", "undefined", "timeStamp", "Date", "now", "sendEvent", "onGestureHandlerEvent", "onGestureHandlerStateChange", "propsRef", "current", "invokeNullableMethod", "cancelPendingGestures", "values", "cancelEvent", "notifyPendingGestures", "onGestureEnded", "forceInvalidate", "Hammer", "INPUT_CANCEL", "isFinal", "onRawEvent", "<PERSON><PERSON><PERSON><PERSON>", "shouldUseTouchEvents", "simultaneousHandlers", "some", "handler", "<PERSON><PERSON><PERSON><PERSON>", "SUPPORTS_TOUCH", "window", "findNodeHandle", "Manager", "inputClass", "TouchInput", "getHammerConfig", "add", "on", "ev", "rotation", "setTimeout", "setupEvents", "onStart", "onGestureActivated", "deltaX", "deltaY", "__initialX", "__initialY", "onSuccess", "_getPendingGestures", "length", "stillWaiting", "filter", "pointers", "minPointers", "enable", "recognizer", "inputData", "options", "_stillWaiting", "deltaRotation", "failed", "simulateCancelEvent", "params", "_inputData", "minDist", "minDistSq", "minVelocity", "minVelocitySq", "maxDist", "maxDistSq", "asArray", "map", "<PERSON><PERSON><PERSON><PERSON>", "v", "ghQueueMicrotask", "configProps", "for<PERSON>ach", "prop", "Number", "NaN", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "index", "key", "value", "entries", "nativeValue", "setValue", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/GestureHandler.ts"], "mappings": ";;;;;;AAEA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAC,uBAAA,CAAAL,OAAA;AACA,IAAAM,iBAAA,GAAAN,OAAA;AAAuD,SAAAK,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAT,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAK,UAAA,GAAAL,CAAA,KAAAU,OAAA,EAAAV,CAAA;AARvD;AACA;;AASA;;AA2BA,IAAImB,gBAAgB,GAAG,CAAC;AAExB,MAAeC,cAAc,CAAC;EAErBC,gBAAgB,GAAG,KAAK;EACxBC,IAAI,GAAkB,IAAI;EAEvBC,gBAAgB,GAAG,KAAK;EACxBC,MAAM,GAAyB,IAAI;EACnCC,eAAe,GAAkB,IAAI;EAGrCC,MAAM,GAAW,CAAC,CAAC;EACnBC,aAAa,GAAUC,YAAK,CAACC,YAAY;EAC3CC,eAAe,GAAyB,CAAC,CAAC;EAC1CC,QAAQ,GAAUH,YAAK,CAACC,YAAY;EACpCG,aAAa,GAAiB,IAAI;EAQ1C,IAAIC,EAAEA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,eAAe,EAAE;EAC9C;;EAEA;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAO,KAAK;EACd;EAEA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,KAAK;EACd;EAEA,IAAIC,0BAA0BA,CAAA,EAAY;IACxC,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EAEAC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACL,eAAe,GAAGhB,gBAAgB,EAAE;IACzC,IAAI,CAACsB,2BAA2B,GAAG,KAAK;EAC1C;EAEAC,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,MAAM;EACpB;EAEAiB,cAAcA,CAACC,QAAc,EAAE,CAAC;EAEhCC,oBAAoBA,CAACZ,EAAU,EAAE;IAC/B,OAAO,IAAI,CAACH,eAAe,CAACG,EAAE,CAAC;EACjC;EAEAa,iBAAiBA,CAACC,OAAa,EAAE;IAC/B,IAAI,CAACjB,eAAe,CAACiB,OAAO,CAACd,EAAE,CAAC,GAAGc,OAAO;EAC5C;EAEAC,wBAAwBA,CACtBC,OAAY,EACZC,WAAgB,EAChBC,MAAW,EAC8B;IACzC,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;EAC1B;EAEA,IAAIC,kBAAkBA,CAAA,EAAqB;IACzC,MAAM,IAAId,KAAK,CAAC,iDAAiD,CAAC;EACpE;EAEAe,iCAAiCA,CAACL,OAAe,EAAE;IACjD,OAAO,IAAI;EACb;EAEAM,kBAAkB,GAAGA,CAAA,KAAM;IACzB,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC/B,MAAM,CAACgC,OAAO,CAAC,EAAE;MACtC,KAAK,MAAMX,OAAO,IAAI,IAAI,CAACrB,MAAM,CAACgC,OAAO,EAAE;QACzCX,OAAO,CAACF,oBAAoB,CAAC,IAAI,CAACZ,EAAE,CAAC;MACvC;IACF;EACF,CAAC;EAED0B,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAM,CAAC,EAAE;IAChD,IAAI,CAACN,kBAAkB,CAAC,CAAC;IAEzB,IAAI,CAAC7B,MAAM,GAAG,IAAI,CAACoC,YAAY,CAAC;MAAEF,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IACtD,IAAI,CAACpB,2BAA2B,GAAG,IAAI,CAACa,iCAAiC,CACvE,IAAI,CAAC5B,MACP,CAAC;IACD,IAAI8B,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC/B,MAAM,CAACgC,OAAO,CAAC,EAAE;MACtC,KAAK,MAAMX,OAAO,IAAI,IAAI,CAACrB,MAAM,CAACgC,OAAO,EAAE;QACzCX,OAAO,CAACD,iBAAiB,CAAC,IAAI,CAAC;MACjC;IACF;IAEA,IAAI,IAAI,CAACtB,MAAM,EAAE;MACf,IAAI,CAACuC,IAAI,CAAC,CAAC;IACb;IACA,OAAO,IAAI,CAACrC,MAAM;EACpB;EAEAsC,OAAO,GAAGA,CAAA,KAAM;IACd,IAAI,CAACT,kBAAkB,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC/B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACyC,IAAI,CAAC,KAAK,CAAC;MACvB,IAAI,CAACzC,MAAM,CAACwC,OAAO,CAAC,CAAC;IACvB;IACA,IAAI,CAACxC,MAAM,GAAG,IAAI;EACpB,CAAC;EAED0C,aAAa,GAAGA,CAAC;IAAEC,CAAC;IAAEC;EAA4B,CAAC,KAAK;IACtD;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC/C,IAAI,CAAEgD,qBAAqB,CAAC,CAAC;IAC/C,MAAMC,aAAa,GACjBJ,CAAC,IAAIE,IAAI,CAACG,IAAI,IAAIL,CAAC,IAAIE,IAAI,CAACI,KAAK,IAAIL,CAAC,IAAIC,IAAI,CAACK,GAAG,IAAIN,CAAC,IAAIC,IAAI,CAACM,MAAM;IACxE,OAAOJ,aAAa;EACtB,CAAC;EAEDK,QAAQA,CAACC,IAA2B,EAAS;IAC3C;IACA,IAAIA,IAAI,IAAI,CAAC,EAAE;MACb,OAAO,CAAC;IACV;IACA,OAAOC,mBAAQ,CAACD,IAAI,CAAC;EACvB;EAEAE,kBAAkBA,CAACC,KAAqB,EAAE;IACxC,MAAM;MAAEC,SAAS;MAAEC,WAAW,EAAEC;IAAiB,CAAC,GAAGH,KAAK;IAC1D;IACA,MAAMI,YAAY,GAAGJ,KAAK,CAACK,eAAe,CAAC,CAAC,CAAC;IAC7C,MAAMd,aAAa,GAAG,IAAI,CAACL,aAAa,CAAC;MACvCC,CAAC,EAAEiB,YAAY,CAACE,OAAO;MACvBlB,CAAC,EAAEgB,YAAY,CAACG;IAClB,CAAC,CAAC;;IAEF;IACA,MAAMC,KAAK,GAAG,IAAI,CAACZ,QAAQ,CAACK,SAA0B,CAAC;IACvD,IAAIO,KAAK,KAAK,IAAI,CAAC7D,aAAa,EAAE;MAChC,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACJ,aAAa;MAClC,IAAI,CAACA,aAAa,GAAG6D,KAAK;IAC5B;IAEA,OAAO;MACLC,WAAW,EAAE;QACXN,gBAAgB;QAChBK,KAAK;QACLjB,aAAa;QACb,GAAG,IAAI,CAACmB,oBAAoB,CAACV,KAAK,CAAC;QACnC;QACAW,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,MAAM,EAAE,IAAI,CAACC,GAAG;QAChB;QACA;QACA;QACA9D,QAAQ,EACNyD,KAAK,KAAK,IAAI,CAAC7D,aAAa,IAAI6D,KAAK,IAAI,CAAC,GACtC,IAAI,CAACzD,QAAQ,GACb+D;MACR,CAAC;MACDC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;EAEAP,oBAAoBA,CAACvC,MAAsB,EAAE;IAC3C,OAAO,CAAC,CAAC;EACX;EAEA+C,SAAS,GAAIT,WAA2B,IAAK;IAC3C,MAAM;MAAEU,qBAAqB;MAAEC;IAA4B,CAAC,GAC1D,IAAI,CAACC,QAAQ,CAACC,OAAO;IAEvB,MAAMtB,KAAK,GAAG,IAAI,CAACD,kBAAkB,CAACU,WAAW,CAAC;IAElDc,oBAAoB,CAACJ,qBAAqB,EAAEnB,KAAK,CAAC;IAClD,IAAI,IAAI,CAAChD,aAAa,KAAKgD,KAAK,CAACS,WAAW,CAACD,KAAK,EAAE;MAClD,IAAI,CAACxD,aAAa,GAAGgD,KAAK,CAACS,WAAW,CAACD,KAAc;MACrDe,oBAAoB,CAACH,2BAA2B,EAAEpB,KAAK,CAAC;IAC1D;EACF,CAAC;EAEDwB,qBAAqBA,CAACxB,KAAqB,EAAE;IAC3C,KAAK,MAAMjC,OAAO,IAAI/B,MAAM,CAACyF,MAAM,CAAC,IAAI,CAAC3E,eAAe,CAAC,EAAE;MACzD,IAAIiB,OAAO,IAAIA,OAAO,CAAC1B,gBAAgB,EAAE;QACvC0B,OAAO,CAACxB,gBAAgB,GAAG,IAAI;QAC/BwB,OAAO,CAAC2D,WAAW,CAAC1B,KAAK,CAAC;MAC5B;IACF;EACF;EAEA2B,qBAAqBA,CAAA,EAAG;IACtB,KAAK,MAAM5D,OAAO,IAAI/B,MAAM,CAACyF,MAAM,CAAC,IAAI,CAAC3E,eAAe,CAAC,EAAE;MACzD,IAAIiB,OAAO,EAAE;QACXA,OAAO,CAACJ,cAAc,CAAC,IAAI,CAAC;MAC9B;IACF;EACF;;EAEA;EACAiE,cAAcA,CAAC5B,KAAqB,EAAE;IACpC,IAAI,CAAC3D,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACmF,qBAAqB,CAACxB,KAAK,CAAC;EACnC;EAEA6B,eAAeA,CAAC7B,KAAqB,EAAE;IACrC,IAAI,IAAI,CAAC3D,gBAAgB,EAAE;MACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACmF,WAAW,CAAC1B,KAAK,CAAC;IACzB;EACF;EAEA0B,WAAWA,CAAC1B,KAAqB,EAAE;IACjC,IAAI,CAAC2B,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACT,SAAS,CAAC;MACb,GAAGlB,KAAK;MACRC,SAAS,EAAE6B,iBAAM,CAACC,YAAY;MAC9BC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACJ,cAAc,CAAC5B,KAAK,CAAC;EAC5B;EAEAiC,UAAUA,CAAC;IAAEC;EAAwB,CAAC,EAAE;IACtC,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC3F,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEA4F,oBAAoBA,CAACzF,MAAc,EAAE;IACnC,OACEA,MAAM,CAAC0F,oBAAoB,EAAEC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAAClF,QAAQ,CAAC,IAAI,KAAK;EAE7E;EAEAmF,OAAOA,CAAC1B,GAA2C,EAAEQ,QAAa,EAAE;IAClE,IAAIR,GAAG,IAAI,IAAI,EAAE;MACf,IAAI,CAAC7B,OAAO,CAAC,CAAC;MACd,IAAI,CAAC1C,IAAI,GAAG,IAAI;MAChB;IACF;;IAEA;IACA,MAAMkG,cAAc,GAAG,cAAc,IAAIC,MAAM;IAC/C,IAAI,CAACpB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACR,GAAG,GAAGA,GAAG;;IAEd;IACA,IAAI,CAACvE,IAAI,GAAG,IAAAoG,2BAAc,EAAC7B,GAAG,CAAC;;IAE/B;IACA;IACA;IACA,IAAI,CAACrE,MAAM,GACTgG,cAAc,IAAI,IAAI,CAACL,oBAAoB,CAAC,IAAI,CAACzF,MAAM,CAAC,GACpD,IAAIoF,iBAAM,CAACa,OAAO,CAAC,IAAI,CAACrG,IAAI,EAAS;MACnCsG,UAAU,EAAEd,iBAAM,CAACe;IACrB,CAAC,CAAC,GACF,IAAIf,iBAAM,CAACa,OAAO,CAAC,IAAI,CAACrG,IAAW,CAAC;IAE1C,IAAI,CAACS,QAAQ,GAAGH,YAAK,CAACC,YAAY;IAClC,IAAI,CAACF,aAAa,GAAGC,YAAK,CAACC,YAAY;IACvC,IAAI,CAACG,aAAa,GAAG,IAAI;IAEzB,MAAM;MAAEqB;IAAmB,CAAC,GAAG,IAAI;IACnC;IACA,MAAMN,OAAO,GAAG,IAAIM,kBAAkB,CAAC,IAAI,CAACyE,eAAe,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACtG,MAAM,CAACuG,GAAG,CAAChF,OAAO,CAAC;IAExB,IAAI,CAACvB,MAAM,CAACwG,EAAE,CAAC,cAAc,EAAGC,EAAe,IAAK;MAClD,IAAI,CAAC,IAAI,CAACvG,MAAM,CAACkC,OAAO,EAAE;QACxB,IAAI,CAACrC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACF,gBAAgB,GAAG,KAAK;QAC7B;MACF;MAEA,IAAI,CAAC4F,UAAU,CAACgB,EAA+B,CAAC;;MAEhD;MACA;MACA,IAAI,IAAI,CAACxG,eAAe,KAAK,IAAI,IAAIwG,EAAE,CAACC,QAAQ,KAAK,CAAC,EAAE;QACtD,IAAI,CAACzG,eAAe,GAAGwG,EAAE,CAACC,QAAQ;MACpC;MACA,IAAID,EAAE,CAACjB,OAAO,EAAE;QACd;QACAmB,UAAU,CAAC,MAAM;UACf,IAAI,CAAC1G,eAAe,GAAG,IAAI;UAC3B,IAAI,CAACF,gBAAgB,GAAG,KAAK;QAC/B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAAC6G,WAAW,CAAC,CAAC;IAClB,IAAI,CAACrE,IAAI,CAAC,CAAC;EACb;EAEAqE,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAAC,IAAI,CAAC/F,UAAU,EAAE;MACpB,IAAI,CAACb,MAAM,CAAEwG,EAAE,CAAC,GAAG,IAAI,CAAC9F,IAAI,OAAO,EAAG8C,KAAkB,IACtD,IAAI,CAACqD,OAAO,CAACrD,KAAkC,CACjD,CAAC;MACD,IAAI,CAACxD,MAAM,CAAEwG,EAAE,CACb,GAAG,IAAI,CAAC9F,IAAI,OAAO,IAAI,CAACA,IAAI,QAAQ,EACnC8C,KAAkB,IAAK;QACtB,IAAI,CAAC4B,cAAc,CAAC5B,KAAkC,CAAC;MACzD,CACF,CAAC;IACH;IACA,IAAI,CAACxD,MAAM,CAAEwG,EAAE,CAAC,IAAI,CAAC9F,IAAI,EAAG+F,EAAe,IACzC,IAAI,CAACK,kBAAkB,CAACL,EAA+B,CACzD,CAAC,CAAC,CAAC;EACL;EAEAI,OAAOA,CAAC;IAAEE,MAAM;IAAEC,MAAM;IAAEN;EAAyB,CAAC,EAAE;IACpD;IACA,IAAI,CAACnG,QAAQ,GAAGH,YAAK,CAACC,YAAY;IAClC,IAAI,CAACF,aAAa,GAAGC,YAAK,CAACC,YAAY;IACvC,IAAI,CAACG,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACX,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACoH,UAAU,GAAGF,MAAM;IACxB,IAAI,CAACG,UAAU,GAAGF,MAAM;IACxB,IAAI,CAAC/G,eAAe,GAAGyG,QAAQ;EACjC;EAEAI,kBAAkBA,CAACL,EAAkB,EAAE;IACrC,IAAI,CAAC/B,SAAS,CAAC+B,EAAE,CAAC;EACpB;EAEAU,SAASA,CAAA,EAAG,CAAC;EAEbC,mBAAmBA,CAAA,EAAG;IACpB,IAAIpF,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC/B,MAAM,CAACgC,OAAO,CAAC,IAAI,IAAI,CAAChC,MAAM,CAACgC,OAAO,CAACmF,MAAM,EAAE;MACpE;MACA;MACA,MAAMC,YAAY,GAAG,IAAI,CAACpH,MAAM,CAACgC,OAAO,CAACqF,MAAM,CAC7C,CAAC;QAAExH;MAAiB,CAAC,KAAKA,gBAAgB,KAAK,KACjD,CAAC;MACD,OAAOuH,YAAY;IACrB;IACA,OAAO,EAAE;EACX;EAEAhB,eAAeA,CAAA,EAAG;IAChB,MAAMkB,QAAQ,GACZ,IAAI,CAACtH,MAAM,CAACuH,WAAW,KAAK,IAAI,CAACvH,MAAM,CAACwD,WAAW,GAC/C,IAAI,CAACxD,MAAM,CAACuH,WAAW,GACvB,CAAC;IACP,OAAO;MACLD;IACF,CAAC;EACH;EAEAjF,IAAI,GAAGA,CAAA,KAAM;IACX,MAAMhB,OAAO,GAAG,IAAI,CAACvB,MAAM,CAAEZ,GAAG,CAAC,IAAI,CAACsB,IAAI,CAAC;IAC3C,IAAI,CAACa,OAAO,EAAE;IAEd,MAAMmG,MAAM,GAAGA,CAACC,UAAe,EAAEC,SAAc,KAAK;MAClD,IAAI,CAAC,IAAI,CAAC1H,MAAM,CAACkC,OAAO,EAAE;QACxB,IAAI,CAACvC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACE,gBAAgB,GAAG,KAAK;QAC7B,OAAO,KAAK;MACd;;MAEA;MACA,IACE,CAAC6H,SAAS,IACV,CAACD,UAAU,CAACE,OAAO,IACnB,OAAOD,SAAS,CAAClE,WAAW,KAAK,WAAW,EAC5C;QACA,OAAO,IAAI,CAAC5C,0BAA0B;MACxC;MAEA,IAAI,IAAI,CAACf,gBAAgB,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAI,CAAC,IAAI,CAACc,UAAU,EAAE;QACpB,IAAI,IAAI,CAAChB,gBAAgB,EAAE;UACzB,OAAO,IAAI;QACb;QACA;QACA;QACA,IAAI,CAACiI,aAAa,GAAG,IAAI,CAACV,mBAAmB,CAAC,CAAC;QAC/C;QACA,IAAI,IAAI,CAACU,aAAa,CAACT,MAAM,EAAE;UAC7B;UACA;UACA,KAAK,MAAM9F,OAAO,IAAI,IAAI,CAACuG,aAAa,EAAE;YACxC;YACA,IAAI,CAACvG,OAAO,CAACV,UAAU,IAAIU,OAAO,CAAC1B,gBAAgB,EAAE;cACnD,IAAI,CAACE,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACF,gBAAgB,GAAG,KAAK;cAC7B,OAAO,KAAK;YACd;UACF;UACA;UACA,OAAO,KAAK;QACd;MACF;;MAEA;MACA,IAAI,CAAC,IAAI,CAACoB,2BAA2B,EAAE;QACrC,OAAO,IAAI;MACb;MAEA,MAAM8G,aAAa,GACjB,IAAI,CAAC9H,eAAe,IAAI,IAAI,GACxB,CAAC,GACD2H,SAAS,CAAClB,QAAQ,GAAG,IAAI,CAACzG,eAAe;MAC/C;MACA,MAAM;QAAE2B,OAAO;QAAEoG;MAAO,CAAC,GAAG,IAAI,CAACxG,wBAAwB,CACvD,IAAI,CAACN,SAAS,CAAC,CAAC,EAChByG,UAAU,EACV;QACE,GAAGC,SAAS;QACZG;MACF,CACF,CAAC;MAED,IAAIC,MAAM,EAAE;QACV,IAAI,CAACC,mBAAmB,CAACL,SAAS,CAAC;QACnC,IAAI,CAAC7H,gBAAgB,GAAG,IAAI;MAC9B;MACA,OAAO6B,OAAO;IAChB,CAAC;IAED,MAAMsG,MAAM,GAAG,IAAI,CAAC5B,eAAe,CAAC,CAAC;IACrC;IACA/E,OAAO,CAAClC,GAAG,CAAC;MAAE,GAAG6I,MAAM;MAAER;IAAO,CAAC,CAAC;EACpC,CAAC;EAEDO,mBAAmBA,CAACE,UAAe,EAAE,CAAC;;EAEtC;EACA7F,YAAYA,CAACpC,MAAc,EAAoB;IAC7C,MAAMmC,KAAK,GAAG;MAAE,GAAGnC;IAAO,CAAC;;IAE3B;IACA,IAAI,SAAS,IAAIA,MAAM,EAAE;MACvBmC,KAAK,CAAC+F,OAAO,GAAGlI,MAAM,CAACkI,OAAO;MAC9B/F,KAAK,CAACgG,SAAS,GAAGhG,KAAK,CAAC+F,OAAO,GAAI/F,KAAK,CAAC+F,OAAQ;IACnD;IACA,IAAI,aAAa,IAAIlI,MAAM,EAAE;MAC3BmC,KAAK,CAACiG,WAAW,GAAGpI,MAAM,CAACoI,WAAW;MACtCjG,KAAK,CAACkG,aAAa,GAAGlG,KAAK,CAACiG,WAAW,GAAIjG,KAAK,CAACiG,WAAY;IAC/D;IACA,IAAI,SAAS,IAAIpI,MAAM,EAAE;MACvBmC,KAAK,CAACmG,OAAO,GAAGtI,MAAM,CAACsI,OAAO;MAC9BnG,KAAK,CAACoG,SAAS,GAAGvI,MAAM,CAACsI,OAAO,GAAItI,MAAM,CAACsI,OAAQ;IACrD;IACA,IAAI,SAAS,IAAItI,MAAM,EAAE;MACvBmC,KAAK,CAACH,OAAO,GAAGwG,OAAO,CAACxI,MAAM,CAACgC,OAAO,CAAC,CACpCyG,GAAG,CAAC,CAAC;QAAExE;MAAmC,CAAC,KAC1C9F,WAAW,CAACuK,UAAU,CAACzE,UAAU,CACnC,CAAC,CACAoD,MAAM,CAAEsB,CAAC,IAAKA,CAAC,CAAC;IACrB,CAAC,MAAM;MACLxG,KAAK,CAACH,OAAO,GAAG,IAAI;IACtB;IACA,IAAI,sBAAsB,IAAIhC,MAAM,EAAE;MACpC,MAAMyF,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAC,IAAI,CAACzF,MAAM,CAAC;MACnEmC,KAAK,CAACuD,oBAAoB,GAAG8C,OAAO,CAACxI,MAAM,CAAC0F,oBAAoB,CAAC,CAC9D+C,GAAG,CAAE7C,OAAgC,IAAK;QACzC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC/B,OAAOzH,WAAW,CAACuK,UAAU,CAAC9C,OAAO,CAAC;QACxC,CAAC,MAAM;UACL,OAAOzH,WAAW,CAACuK,UAAU,CAAC9C,OAAO,CAAC3B,UAAU,CAAC;QACnD;MACF,CAAC,CAAC,CACDoD,MAAM,CAAEsB,CAAC,IAAKA,CAAC,CAAC;MAEnB,IAAIlD,oBAAoB,KAAK,IAAI,CAACA,oBAAoB,CAACtD,KAAK,CAAC,EAAE;QAC7D,IAAAyG,kCAAgB,EAAC,MAAM;UACrB;UACA;UACA,IAAI,CAACtG,OAAO,CAAC,CAAC;UACd,IAAI,CAACuD,OAAO,CAAC,IAAI,CAAC1B,GAAG,EAAE,IAAI,CAACQ,QAAQ,CAAC;QACvC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLxC,KAAK,CAACuD,oBAAoB,GAAG,IAAI;IACnC;IAEA,MAAMmD,WAAW,GAAG,CAClB,aAAa,EACb,aAAa,EACb,SAAS,EACT,SAAS,EACT,WAAW,EACX,eAAe,EACf,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,oBAAoB,EACpB,kBAAkB,EAClB,oBAAoB,EACpB,kBAAkB,CACV;IACVA,WAAW,CAACC,OAAO,CAAEC,IAAkC,IAAK;MAC1D,IAAI,OAAO5G,KAAK,CAAC4G,IAAI,CAAC,KAAK,WAAW,EAAE;QACtC5G,KAAK,CAAC4G,IAAI,CAAC,GAAGC,MAAM,CAACC,GAAG;MAC1B;IACF,CAAC,CAAC;IACF,OAAO9G,KAAK,CAAqB,CAAC;EACpC;AACF;;AAEA;AACA;AACA,SAAS0C,oBAAoBA,CAC3BqE,MAGyC,EACzC5F,KAAkB,EAClB;EACA,IAAI4F,MAAM,EAAE;IACV,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAAC5F,KAAK,CAAC;IACf,CAAC,MAAM;MACL;MACA,IACE,cAAc,IAAI4F,MAAM,IACxB,OAAOA,MAAM,CAACC,YAAY,KAAK,UAAU,EACzC;QACA,MAAMvD,OAAO,GAAGsD,MAAM,CAACC,YAAY,CAAC,CAAC;QACrCtE,oBAAoB,CAACe,OAAO,EAAEtC,KAAK,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,cAAc,IAAI4F,MAAM,EAAE;UAC5B,MAAM;YAAEE;UAAW,CAAC,GAAGF,MAAM,CAACG,YAAY;UAC1C,IAAIvH,KAAK,CAACC,OAAO,CAACqH,UAAU,CAAC,EAAE;YAC7B,KAAK,MAAM,CAACE,KAAK,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,CAAC,IAAIJ,UAAU,CAACK,OAAO,CAAC,CAAC,EAAE;cACxD,IAAIF,GAAG,IAAIjG,KAAK,CAACS,WAAW,EAAE;gBAC5B;gBACA,MAAM2F,WAAW,GAAGpG,KAAK,CAACS,WAAW,CAACwF,GAAG,CAAC;gBAC1C,IAAIC,KAAK,IAAIA,KAAK,CAACG,QAAQ,EAAE;kBAC3B;kBACAH,KAAK,CAACG,QAAQ,CAACD,WAAW,CAAC;gBAC7B,CAAC,MAAM;kBACL;kBACAR,MAAM,CAACG,YAAY,CAACD,UAAU,CAACE,KAAK,CAAC,GAAG,CAACC,GAAG,EAAEG,WAAW,CAAC;gBAC5D;cACF;YACF;UACF;QACF;MACF;IACF;EACF;AACF;AAEA,SAASlB,OAAOA,CAAIgB,KAAc,EAAE;EAClC;EACA,OAAOA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG1H,KAAK,CAACC,OAAO,CAACyH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACpE;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAA7K,OAAA,GAEcU,cAAc", "ignoreList": []}