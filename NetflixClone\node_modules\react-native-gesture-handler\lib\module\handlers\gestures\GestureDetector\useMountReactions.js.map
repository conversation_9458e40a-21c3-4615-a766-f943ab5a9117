{"version": 3, "names": ["transformIntoHandlerTags", "MountRegistry", "useEffect", "shouldUpdateDetector", "relation", "gesture", "undefined", "tag", "handlerTag", "useMountReactions", "updateDetector", "state", "addMountListener", "attachedGesture", "attachedGestures", "blocksHandlers", "config", "requireToFail", "simultaneousWith"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useMountReactions.ts"], "mappings": ";;AAAA,SAASA,wBAAwB,QAAQ,aAAa;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,SAAS,QAAQ,OAAO;AAGjC,SAASC,oBAAoBA,CAC3BC,QAAkC,EAClCC,OAA+B,EAC/B;EACA,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1B,OAAO,KAAK;EACd;EAEA,KAAK,MAAMC,GAAG,IAAIP,wBAAwB,CAACI,QAAQ,CAAC,EAAE;IACpD,IAAIG,GAAG,KAAKF,OAAO,CAACG,UAAU,EAAE;MAC9B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,OAAO,SAASC,iBAAiBA,CAC/BC,cAA0B,EAC1BC,KAA2B,EAC3B;EACAT,SAAS,CAAC,MAAM;IACd,OAAOD,aAAa,CAACW,gBAAgB,CAAEP,OAAO,IAAK;MACjD;MACA;MACA;MACA,KAAK,MAAMQ,eAAe,IAAIF,KAAK,CAACG,gBAAgB,EAAE;QACpD,MAAMC,cAAc,GAAGF,eAAe,CAACG,MAAM,CAACD,cAAc;QAC5D,MAAME,aAAa,GAAGJ,eAAe,CAACG,MAAM,CAACC,aAAa;QAC1D,MAAMC,gBAAgB,GAAGL,eAAe,CAACG,MAAM,CAACE,gBAAgB;QAEhE,IACEf,oBAAoB,CAACY,cAAc,EAAEV,OAAO,CAAC,IAC7CF,oBAAoB,CAACc,aAAa,EAAEZ,OAAO,CAAC,IAC5CF,oBAAoB,CAACe,gBAAgB,EAAEb,OAAO,CAAC,EAC/C;UACAK,cAAc,CAAC,CAAC;;UAEhB;UACA;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,cAAc,EAAEC,KAAK,CAAC,CAAC;AAC7B", "ignoreList": []}