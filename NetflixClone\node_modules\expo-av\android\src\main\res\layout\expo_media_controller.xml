<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:background="#CC000000"
              android:layout_width="match_parent"
              android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="4dip"
        android:orientation="horizontal">
        <ImageButton
            android:id="@+id/skip_previous_button"
            style="@android:style/MediaButton.Previous"
            android:contentDescription="@string/exo_controls_previous_description"/>

        <ImageButton
            android:id="@+id/rewind_button"
            style="@android:style/MediaButton.Rew"
            android:contentDescription="@string/exo_controls_rewind_description"/>

        <ImageButton
            android:id="@+id/play_button"
            style="@android:style/MediaButton.Play"
            android:contentDescription="@string/exo_controls_play_description"/>

        <ImageButton
            android:id="@+id/fast_forward_button"
            style="@android:style/MediaButton.Ffwd"
            android:contentDescription="@string/exo_controls_fastforward_description"/>

        <ImageButton
            android:id="@+id/skip_next_button"
            style="@android:style/MediaButton.Next"
            android:contentDescription="@string/exo_controls_next_description"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/current_time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingStart="10dip"
            android:paddingEnd="4dip"
            android:paddingTop="5dip"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:text="@string/default_media_controller_time"
            android:textStyle="bold"/>

        <SeekBar
            android:id="@+id/seek_bar"
            android:layout_width="wrap_content"
            android:layout_height="32dip"
            android:minHeight="5dp"
            android:maxHeight="5dp"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/end_time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingStart="4dip"
            android:paddingEnd="0dip"
            android:paddingTop="5dip"
            android:text="@string/default_media_controller_time"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"/>

        <ImageButton
            android:id="@+id/fullscreen_mode_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginTop="-5dip"
            android:layout_weight="0"
            android:background="@android:color/transparent"
            android:contentDescription="@string/enter_fullscreen_mode"
            android:paddingEnd="4dip"
            android:paddingStart="6dip"
            android:src="@drawable/ic_fullscreen_32dp"
            android:paddingTop="4dip"/>
    </LinearLayout>
</LinearLayout>