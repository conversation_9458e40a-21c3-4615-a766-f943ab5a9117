{"version": 3, "names": ["numberAsInset", "value", "left", "right", "top", "bottom", "exports", "addInsets", "a", "b", "touchDataToPressEvent", "data", "timestamp", "targetId", "identifier", "id", "locationX", "x", "locationY", "y", "pageX", "absoluteX", "pageY", "absoluteY", "target", "touches", "changedTouches", "gestureToPressEvent", "event", "handlerTag", "isTouchWithinInset", "dimensions", "inset", "touch", "width", "height", "gestureToPressableEvent", "Date", "now", "pressEvent", "nativeEvent", "force", "undefined", "gestureTouchToPressableEvent", "touchesList", "allTouches", "map", "changedTouchesList", "at"], "sourceRoot": "../../../../src", "sources": ["components/Pressable/utils.ts"], "mappings": ";;;;;;AAgBA,MAAMA,aAAa,GAAIC,KAAa,KAAc;EAChDC,IAAI,EAAED,KAAK;EACXE,KAAK,EAAEF,KAAK;EACZG,GAAG,EAAEH,KAAK;EACVI,MAAM,EAAEJ;AACV,CAAC,CAAC;AAACK,OAAA,CAAAN,aAAA,GAAAA,aAAA;AAEH,MAAMO,SAAS,GAAGA,CAACC,CAAS,EAAEC,CAAS,MAAc;EACnDP,IAAI,EAAE,CAACM,CAAC,CAACN,IAAI,IAAI,CAAC,KAAKO,CAAC,CAACP,IAAI,IAAI,CAAC,CAAC;EACnCC,KAAK,EAAE,CAACK,CAAC,CAACL,KAAK,IAAI,CAAC,KAAKM,CAAC,CAACN,KAAK,IAAI,CAAC,CAAC;EACtCC,GAAG,EAAE,CAACI,CAAC,CAACJ,GAAG,IAAI,CAAC,KAAKK,CAAC,CAACL,GAAG,IAAI,CAAC,CAAC;EAChCC,MAAM,EAAE,CAACG,CAAC,CAACH,MAAM,IAAI,CAAC,KAAKI,CAAC,CAACJ,MAAM,IAAI,CAAC;AAC1C,CAAC,CAAC;AAACC,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAEH,MAAMG,qBAAqB,GAAGA,CAC5BC,IAAe,EACfC,SAAiB,EACjBC,QAAgB,MACS;EACzBC,UAAU,EAAEH,IAAI,CAACI,EAAE;EACnBC,SAAS,EAAEL,IAAI,CAACM,CAAC;EACjBC,SAAS,EAAEP,IAAI,CAACQ,CAAC;EACjBC,KAAK,EAAET,IAAI,CAACU,SAAS;EACrBC,KAAK,EAAEX,IAAI,CAACY,SAAS;EACrBC,MAAM,EAAEX,QAAQ;EAChBD,SAAS,EAAEA,SAAS;EACpBa,OAAO,EAAE,EAAE;EAAE;EACbC,cAAc,EAAE,EAAE,CAAE;AACtB,CAAC,CAAC;AAEF,MAAMC,mBAAmB,GAAGA,CAC1BC,KAEC,EACDhB,SAAiB,EACjBC,QAAgB,MACS;EACzBC,UAAU,EAAEc,KAAK,CAACC,UAAU;EAC5Bb,SAAS,EAAEY,KAAK,CAACX,CAAC;EAClBC,SAAS,EAAEU,KAAK,CAACT,CAAC;EAClBC,KAAK,EAAEQ,KAAK,CAACP,SAAS;EACtBC,KAAK,EAAEM,KAAK,CAACL,SAAS;EACtBC,MAAM,EAAEX,QAAQ;EAChBD,SAAS,EAAEA,SAAS;EACpBa,OAAO,EAAE,EAAE;EAAE;EACbC,cAAc,EAAE,EAAE,CAAE;AACtB,CAAC,CAAC;AAEF,MAAMI,kBAAkB,GAAGA,CACzBC,UAA+B,EAC/BC,KAAa,EACbC,KAA2B,KAE3B,CAACA,KAAK,EAAEjB,SAAS,IAAI,CAAC,IAAI,CAACgB,KAAK,CAAC7B,KAAK,IAAI,CAAC,IAAI4B,UAAU,CAACG,KAAK,IAC/D,CAACD,KAAK,EAAEf,SAAS,IAAI,CAAC,IAAI,CAACc,KAAK,CAAC3B,MAAM,IAAI,CAAC,IAAI0B,UAAU,CAACI,MAAM,IACjE,CAACF,KAAK,EAAEjB,SAAS,IAAI,CAAC,IAAI,EAAEgB,KAAK,CAAC9B,IAAI,IAAI,CAAC,CAAC,IAC5C,CAAC+B,KAAK,EAAEf,SAAS,IAAI,CAAC,IAAI,EAAEc,KAAK,CAAC5B,GAAG,IAAI,CAAC,CAAC;AAACE,OAAA,CAAAwB,kBAAA,GAAAA,kBAAA;AAE9C,MAAMM,uBAAuB,GAC3BR,KAEC,IACkB;EACnB,MAAMhB,SAAS,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC;;EAE5B;EACA,MAAMzB,QAAQ,GAAG,CAAC;EAElB,MAAM0B,UAAU,GAAGZ,mBAAmB,CAACC,KAAK,EAAEhB,SAAS,EAAEC,QAAQ,CAAC;EAElE,OAAO;IACL2B,WAAW,EAAE;MACXf,OAAO,EAAE,CAACc,UAAU,CAAC;MACrBb,cAAc,EAAE,CAACa,UAAU,CAAC;MAC5BzB,UAAU,EAAEyB,UAAU,CAACzB,UAAU;MACjCE,SAAS,EAAEY,KAAK,CAACX,CAAC;MAClBC,SAAS,EAAEU,KAAK,CAACT,CAAC;MAClBC,KAAK,EAAEQ,KAAK,CAACP,SAAS;MACtBC,KAAK,EAAEM,KAAK,CAACL,SAAS;MACtBC,MAAM,EAAEX,QAAQ;MAChBD,SAAS,EAAEA,SAAS;MACpB6B,KAAK,EAAEC;IACT;EACF,CAAC;AACH,CAAC;AAACpC,OAAA,CAAA8B,uBAAA,GAAAA,uBAAA;AAEF,MAAMO,4BAA4B,GAChCf,KAAwB,IACL;EACnB,MAAMhB,SAAS,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC;;EAE5B;EACA,MAAMzB,QAAQ,GAAG,CAAC;EAElB,MAAM+B,WAAW,GAAGhB,KAAK,CAACiB,UAAU,CAACC,GAAG,CAAEb,KAAgB,IACxDvB,qBAAqB,CAACuB,KAAK,EAAErB,SAAS,EAAEC,QAAQ,CAClD,CAAC;EACD,MAAMkC,kBAAkB,GAAGnB,KAAK,CAACF,cAAc,CAACoB,GAAG,CAAEb,KAAgB,IACnEvB,qBAAqB,CAACuB,KAAK,EAAErB,SAAS,EAAEC,QAAQ,CAClD,CAAC;EAED,OAAO;IACL2B,WAAW,EAAE;MACXf,OAAO,EAAEmB,WAAW;MACpBlB,cAAc,EAAEqB,kBAAkB;MAClCjC,UAAU,EAAEc,KAAK,CAACC,UAAU;MAC5Bb,SAAS,EAAEY,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAE/B,CAAC,IAAI,CAAC,CAAC;MAC1CC,SAAS,EAAEU,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAE7B,CAAC,IAAI,CAAC,CAAC;MAC1CC,KAAK,EAAEQ,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAE3B,SAAS,IAAI,CAAC,CAAC;MAC9CC,KAAK,EAAEM,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAEzB,SAAS,IAAI,CAAC,CAAC;MAC9CC,MAAM,EAAEX,QAAQ;MAChBD,SAAS,EAAEA,SAAS;MACpB6B,KAAK,EAAEC;IACT;EACF,CAAC;AACH,CAAC;AAACpC,OAAA,CAAAqC,4BAAA,GAAAA,4BAAA", "ignoreList": []}