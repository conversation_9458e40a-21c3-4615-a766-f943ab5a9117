{"version": 3, "names": ["DeviceEventEmitter", "State", "TouchEventType", "<PERSON><PERSON><PERSON><PERSON>", "findOldGestureHandler", "GestureStateManager", "gestureHandlerEventSubscription", "gestureHandlerStateChangeEventSubscription", "gestureStateManagers", "Map", "lastUpdateEvent", "isStateChangeEvent", "event", "oldState", "isTouchEvent", "eventType", "onGestureHandlerEvent", "handler", "handlerTag", "UNDETERMINED", "state", "BEGAN", "handlers", "onBegin", "ACTIVE", "onStart", "END", "onEnd", "onFinalize", "undefined", "FAILED", "CANCELLED", "delete", "has", "set", "create", "manager", "get", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "onUpdate", "onChange", "changeEventCalculator", "<PERSON><PERSON><PERSON><PERSON>", "nativeEvent", "onGestureStateChange", "onGestureEvent", "startListening", "stopListening", "addListener", "remove"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/eventReceiver.ts"], "mappings": ";;AAAA,SAASA,kBAAkB,QAA6B,cAAc;AACtE,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,cAAc,QAAQ,sBAAsB;AAMrD,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,qBAAqB;AAExE,SACEC,mBAAmB,QAEd,uBAAuB;AAE9B,IAAIC,+BAA2D,GAAG,IAAI;AACtE,IAAIC,0CAAsE,GACxE,IAAI;AAEN,MAAMC,oBAA0D,GAAG,IAAIC,GAAG,CAGxE,CAAC;AAEH,MAAMC,eAAmD,GAAG,EAAE;AAE9D,SAASC,kBAAkBA,CACzBC,KAAuE,EACrC;EAClC;EACA,OAAOA,KAAK,CAACC,QAAQ,IAAI,IAAI;AAC/B;AAEA,SAASC,YAAYA,CACnBF,KAAuE,EAC3C;EAC5B,OAAOA,KAAK,CAACG,SAAS,IAAI,IAAI;AAChC;AAEA,OAAO,SAASC,qBAAqBA,CACnCJ,KAAuE,EACvE;EACA,MAAMK,OAAO,GAAGd,WAAW,CAACS,KAAK,CAACM,UAAU,CAE3C;EAED,IAAID,OAAO,EAAE;IACX,IAAIN,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC7B,IACEA,KAAK,CAACC,QAAQ,KAAKZ,KAAK,CAACkB,YAAY,IACrCP,KAAK,CAACQ,KAAK,KAAKnB,KAAK,CAACoB,KAAK,EAC3B;QACAJ,OAAO,CAACK,QAAQ,CAACC,OAAO,GAAGX,KAAK,CAAC;MACnC,CAAC,MAAM,IACL,CAACA,KAAK,CAACC,QAAQ,KAAKZ,KAAK,CAACoB,KAAK,IAC7BT,KAAK,CAACC,QAAQ,KAAKZ,KAAK,CAACkB,YAAY,KACvCP,KAAK,CAACQ,KAAK,KAAKnB,KAAK,CAACuB,MAAM,EAC5B;QACAP,OAAO,CAACK,QAAQ,CAACG,OAAO,GAAGb,KAAK,CAAC;QACjCF,eAAe,CAACO,OAAO,CAACK,QAAQ,CAACJ,UAAU,CAAC,GAAGN,KAAK;MACtD,CAAC,MAAM,IAAIA,KAAK,CAACC,QAAQ,KAAKD,KAAK,CAACQ,KAAK,IAAIR,KAAK,CAACQ,KAAK,KAAKnB,KAAK,CAACyB,GAAG,EAAE;QACtE,IAAId,KAAK,CAACC,QAAQ,KAAKZ,KAAK,CAACuB,MAAM,EAAE;UACnCP,OAAO,CAACK,QAAQ,CAACK,KAAK,GAAGf,KAAK,EAAE,IAAI,CAAC;QACvC;QACAK,OAAO,CAACK,QAAQ,CAACM,UAAU,GAAGhB,KAAK,EAAE,IAAI,CAAC;QAC1CF,eAAe,CAACO,OAAO,CAACK,QAAQ,CAACJ,UAAU,CAAC,GAAGW,SAAS;MAC1D,CAAC,MAAM,IACL,CAACjB,KAAK,CAACQ,KAAK,KAAKnB,KAAK,CAAC6B,MAAM,IAAIlB,KAAK,CAACQ,KAAK,KAAKnB,KAAK,CAAC8B,SAAS,KAChEnB,KAAK,CAACC,QAAQ,KAAKD,KAAK,CAACQ,KAAK,EAC9B;QACA,IAAIR,KAAK,CAACC,QAAQ,KAAKZ,KAAK,CAACuB,MAAM,EAAE;UACnCP,OAAO,CAACK,QAAQ,CAACK,KAAK,GAAGf,KAAK,EAAE,KAAK,CAAC;QACxC;QACAK,OAAO,CAACK,QAAQ,CAACM,UAAU,GAAGhB,KAAK,EAAE,KAAK,CAAC;QAC3CJ,oBAAoB,CAACwB,MAAM,CAACpB,KAAK,CAACM,UAAU,CAAC;QAC7CR,eAAe,CAACO,OAAO,CAACK,QAAQ,CAACJ,UAAU,CAAC,GAAGW,SAAS;MAC1D;IACF,CAAC,MAAM,IAAIf,YAAY,CAACF,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACJ,oBAAoB,CAACyB,GAAG,CAACrB,KAAK,CAACM,UAAU,CAAC,EAAE;QAC/CV,oBAAoB,CAAC0B,GAAG,CACtBtB,KAAK,CAACM,UAAU,EAChBb,mBAAmB,CAAC8B,MAAM,CAACvB,KAAK,CAACM,UAAU,CAC7C,CAAC;MACH;;MAEA;MACA,MAAMkB,OAAO,GAAG5B,oBAAoB,CAAC6B,GAAG,CAACzB,KAAK,CAACM,UAAU,CAAE;MAE3D,QAAQN,KAAK,CAACG,SAAS;QACrB,KAAKb,cAAc,CAACoC,YAAY;UAC9BrB,OAAO,CAACK,QAAQ,EAAEiB,aAAa,GAAG3B,KAAK,EAAEwB,OAAO,CAAC;UACjD;QACF,KAAKlC,cAAc,CAACsC,YAAY;UAC9BvB,OAAO,CAACK,QAAQ,EAAEmB,aAAa,GAAG7B,KAAK,EAAEwB,OAAO,CAAC;UACjD;QACF,KAAKlC,cAAc,CAACwC,UAAU;UAC5BzB,OAAO,CAACK,QAAQ,EAAEqB,WAAW,GAAG/B,KAAK,EAAEwB,OAAO,CAAC;UAC/C;QACF,KAAKlC,cAAc,CAAC0C,iBAAiB;UACnC3B,OAAO,CAACK,QAAQ,EAAEuB,kBAAkB,GAAGjC,KAAK,EAAEwB,OAAO,CAAC;UACtD;MACJ;IACF,CAAC,MAAM;MACLnB,OAAO,CAACK,QAAQ,CAACwB,QAAQ,GAAGlC,KAAK,CAAC;MAElC,IAAIK,OAAO,CAACK,QAAQ,CAACyB,QAAQ,IAAI9B,OAAO,CAACK,QAAQ,CAAC0B,qBAAqB,EAAE;QACvE/B,OAAO,CAACK,QAAQ,CAACyB,QAAQ,GACvB9B,OAAO,CAACK,QAAQ,CAAC0B,qBAAqB,GACpCpC,KAAK,EACLF,eAAe,CAACO,OAAO,CAACK,QAAQ,CAACJ,UAAU,CAC7C,CACF,CAAC;QAEDR,eAAe,CAACO,OAAO,CAACK,QAAQ,CAACJ,UAAU,CAAC,GAAGN,KAAK;MACtD;IACF;EACF,CAAC,MAAM;IACL,MAAMqC,UAAU,GAAG7C,qBAAqB,CAACQ,KAAK,CAACM,UAAU,CAAC;IAC1D,IAAI+B,UAAU,EAAE;MACd,MAAMC,WAAW,GAAG;QAAEA,WAAW,EAAEtC;MAAM,CAAC;MAC1C,IAAID,kBAAkB,CAACC,KAAK,CAAC,EAAE;QAC7BqC,UAAU,CAACE,oBAAoB,CAACD,WAAW,CAAC;MAC9C,CAAC,MAAM;QACLD,UAAU,CAACG,cAAc,CAACF,WAAW,CAAC;MACxC;MACA;IACF;EACF;AACF;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAG;EAC/BC,aAAa,CAAC,CAAC;EAEfhD,+BAA+B,GAAGN,kBAAkB,CAACuD,WAAW,CAC9D,uBAAuB,EACvBvC,qBACF,CAAC;EAEDT,0CAA0C,GAAGP,kBAAkB,CAACuD,WAAW,CACzE,6BAA6B,EAC7BvC,qBACF,CAAC;AACH;AAEA,OAAO,SAASsC,aAAaA,CAAA,EAAG;EAC9B,IAAIhD,+BAA+B,EAAE;IACnCA,+BAA+B,CAACkD,MAAM,CAAC,CAAC;IACxClD,+BAA+B,GAAG,IAAI;EACxC;EAEA,IAAIC,0CAA0C,EAAE;IAC9CA,0CAA0C,CAACiD,MAAM,CAAC,CAAC;IACnDjD,0CAA0C,GAAG,IAAI;EACnD;AACF", "ignoreList": []}