{"version": 3, "file": "useDescriptors.d.ts", "sourceRoot": "", "sources": ["../../../src/useDescriptors.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,YAAY,EACZ,MAAM,EACP,MAAM,2BAA2B,CAAC;AACnC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EACL,KAAK,gBAAgB,EACrB,KAAK,WAAW,EAEjB,MAAM,4BAA4B,CAAC;AAKpC,OAAO,KAAK,EACV,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,WAAW,EACX,SAAS,EACV,MAAM,SAAS,CAAC;AACjB,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAIhE,MAAM,MAAM,sBAAsB,CAChC,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,IAC3B;IACF,IAAI,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC;IAC7B,OAAO,EAAE,CAAC,uBAAuB,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC;IAC5E,MAAM,EAAE,YAAY,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;IAChD,KAAK,EAAE,WAAW,CAChB,aAAa,EACb,MAAM,EACN,KAAK,EACL,aAAa,EACb,QAAQ,EACR,OAAO,CACR,CAAC;CACH,CAAC;AAEF,KAAK,YAAY,CAAC,aAAa,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE;IACpD,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IACxC,OAAO,EAAE,aAAa,CAAC;IACvB,UAAU,EAAE,GAAG,CAAC;IAChB,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC;IAC7B,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAC;CAC9B,KAAK,KAAK,CAAC,YAAY,CAAC;AAEzB,KAAK,uBAAuB,CAAC,aAAa,SAAS,EAAE,IACjD,aAAa,GACb,CAAC,CAAC,KAAK,EAAE;IACP,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IACxC,UAAU,EAAE,GAAG,CAAC;IAChB,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC;CAC9B,KAAK,aAAa,CAAC,CAAC;AAEzB,KAAK,OAAO,CACV,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,IAC3B;IACF,KAAK,EAAE,KAAK,CAAC;IACb,OAAO,EAAE,MAAM,CACb,MAAM,EACN,sBAAsB,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,CACvD,CAAC;IACF,UAAU,EAAE,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAC7C,aAAa,EAAE,uBAAuB,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;IAClE,YAAY,EAAE,YAAY,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;IACtD,QAAQ,EAAE,CAAC,MAAM,EAAE,gBAAgB,KAAK,OAAO,CAAC;IAChD,QAAQ,EAAE,MAAM,KAAK,CAAC;IACtB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACjC,WAAW,EAAE,WAAW,CAAC;IACzB,gBAAgB,EAAE,gBAAgB,CAAC;IACnC,YAAY,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IACpC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACxC,OAAO,EAAE,sBAAsB,CAAC,QAAQ,CAAC,CAAC;CAC3C,CAAC;AAEF;;;;;;;GAOG;AACH,wBAAgB,cAAc,CAC5B,KAAK,SAAS,eAAe,EAC7B,aAAa,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,EAChD,aAAa,SAAS,EAAE,EACxB,QAAQ,SAAS,YAAY,EAC7B,EACA,KAAK,EACL,OAAO,EACP,UAAU,EACV,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,OAAO,GACR,EAAE,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC;sBAoNf,SAAS,CAAC,aAAa,CAAC,eAAe,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BxE"}