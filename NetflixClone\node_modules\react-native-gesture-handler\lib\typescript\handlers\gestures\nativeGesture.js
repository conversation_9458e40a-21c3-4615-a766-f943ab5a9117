import { BaseGesture } from './gesture';
export class NativeGesture extends BaseGesture {
    config = {};
    constructor() {
        super();
        this.handlerName = 'NativeViewGestureHandler';
    }
    /**
     * When true, underlying handler will activate unconditionally when in `BEGAN` or `UNDETERMINED` state.
     * @param value
     */
    shouldActivateOnStart(value) {
        this.config.shouldActivateOnStart = value;
        return this;
    }
    /**
     * When true, cancels all other gesture handlers when this `NativeViewGestureHandler` receives an `ACTIVE` state event.
     * @param value
     */
    disallowInterruption(value) {
        this.config.disallowInterruption = value;
        return this;
    }
}
