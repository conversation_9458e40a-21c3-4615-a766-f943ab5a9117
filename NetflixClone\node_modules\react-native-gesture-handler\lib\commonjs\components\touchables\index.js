"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "TouchableHighlight", {
  enumerable: true,
  get: function () {
    return _TouchableHighlight.default;
  }
});
Object.defineProperty(exports, "TouchableNativeFeedback", {
  enumerable: true,
  get: function () {
    return _TouchableNativeFeedback.default;
  }
});
Object.defineProperty(exports, "TouchableOpacity", {
  enumerable: true,
  get: function () {
    return _TouchableOpacity.default;
  }
});
Object.defineProperty(exports, "TouchableWithoutFeedback", {
  enumerable: true,
  get: function () {
    return _TouchableWithoutFeedback.default;
  }
});
var _TouchableNativeFeedback = _interopRequireDefault(require("./TouchableNativeFeedback"));
var _TouchableWithoutFeedback = _interopRequireDefault(require("./TouchableWithoutFeedback"));
var _TouchableOpacity = _interopRequireDefault(require("./TouchableOpacity"));
var _TouchableHighlight = _interopRequireDefault(require("./TouchableHighlight"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
//# sourceMappingURL=index.js.map