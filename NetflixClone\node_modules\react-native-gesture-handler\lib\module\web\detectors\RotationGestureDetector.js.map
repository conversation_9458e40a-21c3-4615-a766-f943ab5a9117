{"version": 3, "names": ["EventTypes", "RotationGestureDetector", "currentTime", "previousTime", "previousAngle", "_rotation", "_anchorX", "_anchorY", "isInProgress", "keyPointers", "NaN", "constructor", "callbacks", "onRotationBegin", "onRotation", "onRotationEnd", "updateCurrent", "event", "tracker", "time", "firstPointerID", "secondPointerID", "firstPointerCoords", "getLastAbsoluteCoords", "secondPointerCoords", "vectorX", "x", "vectorY", "y", "angle", "Math", "atan2", "Number", "isNaN", "rotation", "PI", "finish", "set<PERSON>eyPointers", "pointerIDs", "trackedPointers", "keys", "next", "value", "onTouchEvent", "eventType", "DOWN", "ADDITIONAL_POINTER_DOWN", "MOVE", "ADDITIONAL_POINTER_UP", "indexOf", "pointerId", "UP", "reset", "anchorX", "anchorY", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../../../src", "sources": ["web/detectors/RotationGestureDetector.ts"], "mappings": ";;AAAA,SAAuBA,UAAU,QAAQ,eAAe;AASxD,eAAe,MAAMC,uBAAuB,CAE5C;EAKUC,WAAW,GAAG,CAAC;EACfC,YAAY,GAAG,CAAC;EAEhBC,aAAa,GAAG,CAAC;EACjBC,SAAS,GAAG,CAAC;EAEbC,QAAQ,GAAG,CAAC;EACZC,QAAQ,GAAG,CAAC;EAEZC,YAAY,GAAG,KAAK;EAEpBC,WAAW,GAAa,CAACC,GAAG,EAAEA,GAAG,CAAC;EAE1CC,WAAWA,CAACC,SAAkC,EAAE;IAC9C,IAAI,CAACC,eAAe,GAAGD,SAAS,CAACC,eAAe;IAChD,IAAI,CAACC,UAAU,GAAGF,SAAS,CAACE,UAAU;IACtC,IAAI,CAACC,aAAa,GAAGH,SAAS,CAACG,aAAa;EAC9C;EAEQC,aAAaA,CAACC,KAAmB,EAAEC,OAAuB,EAAQ;IACxE,IAAI,CAACf,YAAY,GAAG,IAAI,CAACD,WAAW;IACpC,IAAI,CAACA,WAAW,GAAGe,KAAK,CAACE,IAAI;IAE7B,MAAM,CAACC,cAAc,EAAEC,eAAe,CAAC,GAAG,IAAI,CAACZ,WAAW;IAE1D,MAAMa,kBAAkB,GAAGJ,OAAO,CAACK,qBAAqB,CAACH,cAAc,CAAC;IACxE,MAAMI,mBAAmB,GAAGN,OAAO,CAACK,qBAAqB,CAACF,eAAe,CAAC;IAE1E,IAAI,CAACC,kBAAkB,IAAI,CAACE,mBAAmB,EAAE;MAC/C;IACF;IAEA,MAAMC,OAAe,GAAGD,mBAAmB,CAACE,CAAC,GAAGJ,kBAAkB,CAACI,CAAC;IACpE,MAAMC,OAAe,GAAGH,mBAAmB,CAACI,CAAC,GAAGN,kBAAkB,CAACM,CAAC;IAEpE,IAAI,CAACtB,QAAQ,GAAG,CAACgB,kBAAkB,CAACI,CAAC,GAAGF,mBAAmB,CAACE,CAAC,IAAI,CAAC;IAClE,IAAI,CAACnB,QAAQ,GAAG,CAACe,kBAAkB,CAACM,CAAC,GAAGJ,mBAAmB,CAACI,CAAC,IAAI,CAAC;;IAElE;IACA,MAAMC,KAAa,GAAG,CAACC,IAAI,CAACC,KAAK,CAACJ,OAAO,EAAEF,OAAO,CAAC;IAEnD,IAAI,CAACpB,SAAS,GAAG2B,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC7B,aAAa,CAAC,GAC7C,CAAC,GACD,IAAI,CAACA,aAAa,GAAGyB,KAAK;IAE9B,IAAI,CAACzB,aAAa,GAAGyB,KAAK;IAE1B,IAAI,IAAI,CAACK,QAAQ,GAAGJ,IAAI,CAACK,EAAE,EAAE;MAC3B,IAAI,CAAC9B,SAAS,IAAIyB,IAAI,CAACK,EAAE;IAC3B,CAAC,MAAM,IAAI,IAAI,CAACD,QAAQ,GAAG,CAACJ,IAAI,CAACK,EAAE,EAAE;MACnC,IAAI,CAAC9B,SAAS,IAAIyB,IAAI,CAACK,EAAE;IAC3B;IAEA,IAAI,IAAI,CAACD,QAAQ,GAAGJ,IAAI,CAACK,EAAE,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC9B,SAAS,IAAIyB,IAAI,CAACK,EAAE;IAC3B,CAAC,MAAM,IAAI,IAAI,CAACD,QAAQ,GAAG,CAACJ,IAAI,CAACK,EAAE,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC9B,SAAS,IAAIyB,IAAI,CAACK,EAAE;IAC3B;EACF;EAEQC,MAAMA,CAAA,EAAS;IACrB,IAAI,CAAC,IAAI,CAAC5B,YAAY,EAAE;MACtB;IACF;IAEA,IAAI,CAACA,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,WAAW,GAAG,CAACC,GAAG,EAAEA,GAAG,CAAC;IAC7B,IAAI,CAACK,aAAa,CAAC,IAAI,CAAC;EAC1B;EAEQsB,cAAcA,CAACnB,OAAuB,EAAQ;IACpD,IAAI,IAAI,CAACT,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,EAAE;MAC9C;IACF;IAEA,MAAM6B,UAAoC,GAAGpB,OAAO,CAACqB,eAAe,CAACC,IAAI,CAAC,CAAC;IAE3E,IAAI,CAAC/B,WAAW,CAAC,CAAC,CAAC,GAAG6B,UAAU,CAACG,IAAI,CAAC,CAAC,CAACC,KAAe;IACvD,IAAI,CAACjC,WAAW,CAAC,CAAC,CAAC,GAAG6B,UAAU,CAACG,IAAI,CAAC,CAAC,CAACC,KAAe;EACzD;EAEOC,YAAYA,CAAC1B,KAAmB,EAAEC,OAAuB,EAAW;IACzE,QAAQD,KAAK,CAAC2B,SAAS;MACrB,KAAK5C,UAAU,CAAC6C,IAAI;QAClB,IAAI,CAACrC,YAAY,GAAG,KAAK;QACzB;MAEF,KAAKR,UAAU,CAAC8C,uBAAuB;QACrC,IAAI,IAAI,CAACtC,YAAY,EAAE;UACrB;QACF;QACA,IAAI,CAACA,YAAY,GAAG,IAAI;QAExB,IAAI,CAACL,YAAY,GAAGc,KAAK,CAACE,IAAI;QAC9B,IAAI,CAACf,aAAa,GAAGM,GAAG;QAExB,IAAI,CAAC2B,cAAc,CAACnB,OAAO,CAAC;QAE5B,IAAI,CAACF,aAAa,CAACC,KAAK,EAAEC,OAAO,CAAC;QAClC,IAAI,CAACL,eAAe,CAAC,IAAI,CAAC;QAC1B;MAEF,KAAKb,UAAU,CAAC+C,IAAI;QAClB,IAAI,CAAC,IAAI,CAACvC,YAAY,EAAE;UACtB;QACF;QAEA,IAAI,CAACQ,aAAa,CAACC,KAAK,EAAEC,OAAO,CAAC;QAClC,IAAI,CAACJ,UAAU,CAAC,IAAI,CAAC;QAErB;MAEF,KAAKd,UAAU,CAACgD,qBAAqB;QACnC,IAAI,CAAC,IAAI,CAACxC,YAAY,EAAE;UACtB;QACF;QAEA,IAAI,IAAI,CAACC,WAAW,CAACwC,OAAO,CAAChC,KAAK,CAACiC,SAAS,CAAC,IAAI,CAAC,EAAE;UAClD,IAAI,CAACd,MAAM,CAAC,CAAC;QACf;QAEA;MAEF,KAAKpC,UAAU,CAACmD,EAAE;QAChB,IAAI,IAAI,CAAC3C,YAAY,EAAE;UACrB,IAAI,CAAC4B,MAAM,CAAC,CAAC;QACf;QACA;IACJ;IAEA,OAAO,IAAI;EACb;EAEOgB,KAAKA,CAAA,EAAS;IACnB,IAAI,CAAC3C,WAAW,GAAG,CAACC,GAAG,EAAEA,GAAG,CAAC;IAC7B,IAAI,CAACF,YAAY,GAAG,KAAK;EAC3B;EAEA,IAAW6C,OAAOA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC/C,QAAQ;EACtB;EAEA,IAAWgD,OAAOA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC/C,QAAQ;EACtB;EAEA,IAAW2B,QAAQA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC7B,SAAS;EACvB;EAEA,IAAWkD,SAASA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACrD,WAAW,GAAG,IAAI,CAACC,YAAY;EAC7C;AACF", "ignoreList": []}