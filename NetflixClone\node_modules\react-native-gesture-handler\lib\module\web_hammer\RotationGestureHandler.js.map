{"version": 3, "names": ["Hammer", "DEG_RAD", "IndiscreteGestureHandler", "RotationGestureHandler", "name", "NativeGestureClass", "Rotate", "transformNativeEvent", "rotation", "velocity", "center", "initialRotation", "anchorX", "x", "anchorY", "y"], "sourceRoot": "../../../src", "sources": ["web_hammer/RotationGestureHandler.ts"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,gBAAgB;AAEnC,SAASC,OAAO,QAAQ,aAAa;AAErC,OAAOC,wBAAwB,MAAM,4BAA4B;AAEjE,MAAMC,sBAAsB,SAASD,wBAAwB,CAAC;EAC5D,IAAIE,IAAIA,CAAA,EAAG;IACT,OAAO,QAAQ;EACjB;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOL,MAAM,CAACM,MAAM;EACtB;EAEAC,oBAAoBA,CAAC;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAuB,CAAC,EAAE;IACnE,OAAO;MACLF,QAAQ,EAAE,CAACA,QAAQ,IAAI,IAAI,CAACG,eAAe,IAAI,CAAC,CAAC,IAAIV,OAAO;MAC5DW,OAAO,EAAEF,MAAM,CAACG,CAAC;MACjBC,OAAO,EAAEJ,MAAM,CAACK,CAAC;MACjBN;IACF,CAAC;EACH;AACF;AACA,eAAeN,sBAAsB", "ignoreList": []}