{"version": 3, "names": ["getActionFromState", "getActionFromStateDefault", "getStateFromPath", "getStateFromPathDefault", "useNavigationIndependentTree", "React", "Linking", "Platform", "extractPathFromURL", "linkingHandlers", "useLinking", "ref", "enabled", "prefixes", "filter", "config", "getInitialURL", "Promise", "race", "resolve", "setTimeout", "subscribe", "listener", "callback", "url", "subscription", "addEventListener", "removeEventListener", "bind", "remove", "onUnhandledLinking", "independent", "useEffect", "process", "env", "NODE_ENV", "undefined", "length", "console", "error", "OS", "join", "trim", "handler", "Symbol", "push", "index", "indexOf", "splice", "enabledRef", "useRef", "prefixesRef", "filterRef", "configRef", "getInitialURLRef", "getStateFromPathRef", "getActionFromStateRef", "current", "getStateFromURL", "useCallback", "path", "getInitialState", "state", "then", "thenable", "onfulfilled", "catch", "navigation", "rootState", "getRootState", "routes", "some", "r", "routeNames", "includes", "name", "action", "dispatch", "e", "warn", "message", "resetRoot"], "sourceRoot": "../../src", "sources": ["useLinking.native.tsx"], "mappings": ";;AAAA,SACEA,kBAAkB,IAAIC,yBAAyB,EAC/CC,gBAAgB,IAAIC,uBAAuB,EAG3CC,4BAA4B,QACvB,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,QAAQ,cAAc;AAEhD,SAASC,kBAAkB,QAAQ,yBAAsB;AAOzD,MAAMC,eAAyB,GAAG,EAAE;AAEpC,OAAO,SAASC,UAAUA,CACxBC,GAAkE,EAClE;EACEC,OAAO,GAAG,IAAI;EACdC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,aAAa,GAAGA,CAAA,KACdC,OAAO,CAACC,IAAI,CAAC,CACXZ,OAAO,CAACU,aAAa,CAAC,CAAC,EACvB,IAAIC,OAAO,CAAaE,OAAO,IAAK;IAClC;IACA;IACAC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;EAC1B,CAAC,CAAC,CACH,CAAC;EACJE,SAAS,GAAIC,QAAQ,IAAK;IACxB,MAAMC,QAAQ,GAAGA,CAAC;MAAEC;IAAqB,CAAC,KAAKF,QAAQ,CAACE,GAAG,CAAC;IAE5D,MAAMC,YAAY,GAAGnB,OAAO,CAACoB,gBAAgB,CAAC,KAAK,EAAEH,QAAQ,CAEhD;;IAEb;IACA;IACA,MAAMI,mBAAmB,GAAGrB,OAAO,CAACqB,mBAAmB,EAAEC,IAAI,CAACtB,OAAO,CAAC;IAEtE,OAAO,MAAM;MACX;MACA,IAAImB,YAAY,EAAEI,MAAM,EAAE;QACxBJ,YAAY,CAACI,MAAM,CAAC,CAAC;MACvB,CAAC,MAAM;QACLF,mBAAmB,GAAG,KAAK,EAAEJ,QAAQ,CAAC;MACxC;IACF,CAAC;EACH,CAAC;EACDrB,gBAAgB,GAAGC,uBAAuB;EAC1CH,kBAAkB,GAAGC;AACd,CAAC,EACV6B,kBAAqE,EACrE;EACA,MAAMC,WAAW,GAAG3B,4BAA4B,CAAC,CAAC;EAElDC,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAOC,SAAS;IAClB;IAEA,IAAIL,WAAW,EAAE;MACf,OAAOK,SAAS;IAClB;IAEA,IAAIxB,OAAO,KAAK,KAAK,IAAIH,eAAe,CAAC4B,MAAM,EAAE;MAC/CC,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,EAC5DhC,QAAQ,CAACiC,EAAE,KAAK,SAAS,GACrB,sJAAsJ,GACtJ,EAAE,CACP,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,CAAC,CACV,CAAC;IACH;IAEA,MAAMC,OAAO,GAAGC,MAAM,CAAC,CAAC;IAExB,IAAIhC,OAAO,KAAK,KAAK,EAAE;MACrBH,eAAe,CAACoC,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,MAAMG,KAAK,GAAGrC,eAAe,CAACsC,OAAO,CAACJ,OAAO,CAAC;MAE9C,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;QACdrC,eAAe,CAACuC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAAClC,OAAO,EAAEmB,WAAW,CAAC,CAAC;;EAE1B;EACA;EACA;EACA,MAAMkB,UAAU,GAAG5C,KAAK,CAAC6C,MAAM,CAACtC,OAAO,CAAC;EACxC,MAAMuC,WAAW,GAAG9C,KAAK,CAAC6C,MAAM,CAACrC,QAAQ,CAAC;EAC1C,MAAMuC,SAAS,GAAG/C,KAAK,CAAC6C,MAAM,CAACpC,MAAM,CAAC;EACtC,MAAMuC,SAAS,GAAGhD,KAAK,CAAC6C,MAAM,CAACnC,MAAM,CAAC;EACtC,MAAMuC,gBAAgB,GAAGjD,KAAK,CAAC6C,MAAM,CAAClC,aAAa,CAAC;EACpD,MAAMuC,mBAAmB,GAAGlD,KAAK,CAAC6C,MAAM,CAAChD,gBAAgB,CAAC;EAC1D,MAAMsD,qBAAqB,GAAGnD,KAAK,CAAC6C,MAAM,CAAClD,kBAAkB,CAAC;EAE9DK,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpBiB,UAAU,CAACQ,OAAO,GAAG7C,OAAO;IAC5BuC,WAAW,CAACM,OAAO,GAAG5C,QAAQ;IAC9BuC,SAAS,CAACK,OAAO,GAAG3C,MAAM;IAC1BuC,SAAS,CAACI,OAAO,GAAG1C,MAAM;IAC1BuC,gBAAgB,CAACG,OAAO,GAAGzC,aAAa;IACxCuC,mBAAmB,CAACE,OAAO,GAAGvD,gBAAgB;IAC9CsD,qBAAqB,CAACC,OAAO,GAAGzD,kBAAkB;EACpD,CAAC,CAAC;EAEF,MAAM0D,eAAe,GAAGrD,KAAK,CAACsD,WAAW,CACtCnC,GAA8B,IAAK;IAClC,IAAI,CAACA,GAAG,IAAK4B,SAAS,CAACK,OAAO,IAAI,CAACL,SAAS,CAACK,OAAO,CAACjC,GAAG,CAAE,EAAE;MAC1D,OAAOY,SAAS;IAClB;IAEA,MAAMwB,IAAI,GAAGpD,kBAAkB,CAAC2C,WAAW,CAACM,OAAO,EAAEjC,GAAG,CAAC;IAEzD,OAAOoC,IAAI,KAAKxB,SAAS,GACrBmB,mBAAmB,CAACE,OAAO,CAACG,IAAI,EAAEP,SAAS,CAACI,OAAO,CAAC,GACpDrB,SAAS;EACf,CAAC,EACD,EACF,CAAC;EAED,MAAMyB,eAAe,GAAGxD,KAAK,CAACsD,WAAW,CAAC,MAAM;IAC9C,IAAIG,KAA8B;IAElC,IAAIb,UAAU,CAACQ,OAAO,EAAE;MACtB,MAAMjC,GAAG,GAAG8B,gBAAgB,CAACG,OAAO,CAAC,CAAC;MAEtC,IAAIjC,GAAG,IAAI,IAAI,EAAE;QACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UAC3B,OAAOA,GAAG,CAACuC,IAAI,CAAEvC,GAAG,IAAK;YACvB,MAAMsC,KAAK,GAAGJ,eAAe,CAAClC,GAAG,CAAC;YAElC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cAC3B;cACAM,kBAAkB,CAACtB,kBAAkB,CAACK,QAAQ,EAAEW,GAAG,CAAC,CAAC;YACvD;YAEA,OAAOsC,KAAK;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACLhC,kBAAkB,CAACtB,kBAAkB,CAACK,QAAQ,EAAEW,GAAG,CAAC,CAAC;QACvD;MACF;MAEAsC,KAAK,GAAGJ,eAAe,CAAClC,GAAG,CAAC;IAC9B;IAEA,MAAMwC,QAAQ,GAAG;MACfD,IAAIA,CAACE,WAAsD,EAAE;QAC3D,OAAOhD,OAAO,CAACE,OAAO,CAAC8C,WAAW,GAAGA,WAAW,CAACH,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDI,KAAKA,CAAA,EAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;EACjB,CAAC,EAAE,CAACN,eAAe,EAAE5B,kBAAkB,EAAEjB,QAAQ,CAAC,CAAC;EAEnDR,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,MAAMV,QAAQ,GAAIE,GAAW,IAAK;MAChC,IAAI,CAACZ,OAAO,EAAE;QACZ;MACF;MAEA,MAAMuD,UAAU,GAAGxD,GAAG,CAAC8C,OAAO;MAC9B,MAAMK,KAAK,GAAGK,UAAU,GAAGT,eAAe,CAAClC,GAAG,CAAC,GAAGY,SAAS;MAE3D,IAAI+B,UAAU,IAAIL,KAAK,EAAE;QACvB;QACAhC,kBAAkB,CAACtB,kBAAkB,CAACK,QAAQ,EAAEW,GAAG,CAAC,CAAC;QACrD,MAAM4C,SAAS,GAAGD,UAAU,CAACE,YAAY,CAAC,CAAC;QAC3C,IAAIP,KAAK,CAACQ,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAK,CAACJ,SAAS,EAAEK,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC,CAAC,EAAE;UACrE;QACF;QAEA,MAAMC,MAAM,GAAGpB,qBAAqB,CAACC,OAAO,CAACK,KAAK,EAAET,SAAS,CAACI,OAAO,CAAC;QAEtE,IAAImB,MAAM,KAAKxC,SAAS,EAAE;UACxB,IAAI;YACF+B,UAAU,CAACU,QAAQ,CAACD,MAAM,CAAC;UAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;YACV;YACA;YACAxC,OAAO,CAACyC,IAAI,CACV,qDAAqDvD,GAAG,MACtD,OAAOsD,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACE,OAAO,GACTF,CAAC,EAET,CAAC;UACH;QACF,CAAC,MAAM;UACLX,UAAU,CAACc,SAAS,CAACnB,KAAK,CAAC;QAC7B;MACF;IACF,CAAC;IAED,OAAOzC,SAAS,CAACC,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACV,OAAO,EAAE8C,eAAe,EAAE5B,kBAAkB,EAAEjB,QAAQ,EAAEF,GAAG,EAAEU,SAAS,CAAC,CAAC;EAE5E,OAAO;IACLwC;EACF,CAAC;AACH", "ignoreList": []}