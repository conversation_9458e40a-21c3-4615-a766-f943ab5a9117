"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var _UnimplementedView = _interopRequireDefault(require("../../modules/UnimplementedView"));
/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

function YellowBox(props) {
  return /*#__PURE__*/_react.default.createElement(_UnimplementedView.default, props);
}
YellowBox.ignoreWarnings = () => {};
var _default = exports.default = YellowBox;
module.exports = exports.default;