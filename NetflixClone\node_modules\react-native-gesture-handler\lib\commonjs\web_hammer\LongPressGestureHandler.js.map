{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_State", "_PressGestureHandler", "_utils", "e", "__esModule", "default", "LongPressGestureHandler", "PressGestureHandler", "minDurationMs", "isnan", "config", "maxDist", "updateHasCustomActivationCriteria", "maxDistSq", "isValidNumber", "getConfig", "hasCustomActivationCriteria", "shouldCancelWhenOutside", "getHammerConfig", "time", "getState", "type", "Hammer", "INPUT_START", "State", "ACTIVE", "INPUT_MOVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/LongPressGestureHandler.ts"], "mappings": ";;;;;;AAEA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,oBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAA+C,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAN/C;AACA;;AASA,MAAMG,uBAAuB,SAASC,4BAAmB,CAAC;EACxD,IAAIC,aAAaA,CAAA,EAAW;IAC1B;IACA,OAAO,IAAAC,YAAK,EAAC,IAAI,CAACC,MAAM,CAACF,aAAa,CAAC,GAAG,GAAG,GAAG,IAAI,CAACE,MAAM,CAACF,aAAa;EAC3E;EAEA,IAAIG,OAAOA,CAAA,EAAG;IACZ;IACA,OAAO,IAAAF,YAAK,EAAC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;EAC7D;EAEAC,iCAAiCA,CAAC;IAAEC;EAAkB,CAAC,EAAE;IACvD,OAAO,CAAC,IAAAC,oBAAa,EAACD,SAAS,CAAC;EAClC;EAEAE,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE;MACrC;MACA;MACA,OAAO;QACLC,uBAAuB,EAAE,IAAI;QAC7BJ,SAAS,EAAE;MACb,CAAC;IACH;IACA,OAAO,IAAI,CAACH,MAAM;EACpB;EAEAQ,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1B;MACAC,IAAI,EAAE,IAAI,CAACX;IACb,CAAC;EACH;EAEAY,QAAQA,CAACC,IAAmC,EAAE;IAC5C,OAAO;MACL,CAACC,iBAAM,CAACC,WAAW,GAAGC,YAAK,CAACC,MAAM;MAClC,CAACH,iBAAM,CAACI,UAAU,GAAGF,YAAK,CAACC,MAAM;MACjC,CAACH,iBAAM,CAACK,SAAS,GAAGH,YAAK,CAACI,GAAG;MAC7B,CAACN,iBAAM,CAACO,YAAY,GAAGL,YAAK,CAACM;IAC/B,CAAC,CAACT,IAAI,CAAC;EACT;AACF;AAAC,IAAAU,QAAA,GAAAC,OAAA,CAAA3B,OAAA,GAEcC,uBAAuB", "ignoreList": []}