{"version": 3, "names": ["_utils", "require", "_getShadowNodeFromRef", "_react", "_findNodeHandle", "_interopRequireDefault", "e", "__esModule", "default", "useViewRefHandler", "state", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "useCallback", "ref", "viewRef", "previousViewTag", "findNodeHandle", "firstRender", "__DEV__", "isF<PERSON><PERSON>", "global", "isViewFlatteningDisabled", "node", "getShadowNodeFromRef", "console", "error", "tagMessage"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useViewRefHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAGA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,eAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAAqD,SAAAI,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAMrD;AACA;AACA;AACO,SAASG,iBAAiBA,CAC/BC,KAA2B,EAC3BC,sBAA4D,EAC5D;EACA,MAAMC,UAAU,GAAG,IAAAC,kBAAW,EAC3BC,GAA2B,IAAK;IAC/B,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEAJ,KAAK,CAACK,OAAO,GAAGD,GAAG;;IAEnB;IACA,IAAIJ,KAAK,CAACM,eAAe,KAAK,CAAC,CAAC,EAAE;MAChCN,KAAK,CAACM,eAAe,GAAG,IAAAC,uBAAc,EAACP,KAAK,CAACK,OAAO,CAAW;IACjE;;IAEA;IACA;IACA,IAAI,CAACL,KAAK,CAACQ,WAAW,EAAE;MACtBP,sBAAsB,CAAC,IAAI,CAAC;IAC9B;IAEA,IAAIQ,OAAO,IAAI,IAAAC,eAAQ,EAAC,CAAC,IAAIC,MAAM,CAACC,wBAAwB,EAAE;MAC5D,MAAMC,IAAI,GAAG,IAAAC,0CAAoB,EAACV,GAAG,CAAC;MACtC,IAAIO,MAAM,CAACC,wBAAwB,CAACC,IAAI,CAAC,KAAK,KAAK,EAAE;QACnDE,OAAO,CAACC,KAAK,CACX,IAAAC,iBAAU,EACR,oEAAoE,GAClE,kGACJ,CACF,CAAC;MACH;IACF;EACF,CAAC,EACD,CAACjB,KAAK,EAAEC,sBAAsB,CAChC,CAAC;EAED,OAAOC,UAAU;AACnB", "ignoreList": []}