{"version": 3, "file": "ReanimatedSwipeable.d.ts", "sourceRoot": "", "sources": ["../../../src/components/ReanimatedSwipeable.tsx"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,EACZ,YAAY,EAKb,MAAM,OAAO,CAAC;AAOf,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAC;AAE5E,OAAiB,EAEf,WAAW,EASZ,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAGL,SAAS,EAGT,SAAS,EACV,MAAM,cAAc,CAAC;AACtB,OAAO,EAAuC,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAIhF,KAAK,iBAAiB,GAAG,OAAO,CAC9B,MAAM,sBAAsB,EAC5B,gBAAgB,GAAG,sBAAsB,CAC1C,CAAC;AAEF,aAAK,cAAc;IACjB,IAAI,SAAS;IACb,KAAK,UAAU;CAChB;AAED,MAAM,WAAW,cACf,SAAQ,IAAI,CAAC,sBAAsB,EAAE,iBAAiB,CAAC;IACvD;;;;;OAKG;IACH,8BAA8B,CAAC,EAAE,OAAO,CAAC;IAEzC;;;;;OAKG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;;;OAIG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAEhC;;;OAGG;IACH,uBAAuB,CAAC,EAAE,MAAM,CAAC;IAEjC;;;;OAIG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAE3B;;OAEG;IACH,eAAe,CAAC,EAAE,CAChB,SAAS,EAAE,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,KAClD,IAAI,CAAC;IAEV;;OAEG;IACH,gBAAgB,CAAC,EAAE,CACjB,SAAS,EAAE,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,KAClD,IAAI,CAAC;IAEV;;OAEG;IACH,mBAAmB,CAAC,EAAE,CACpB,SAAS,EAAE,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,KAClD,IAAI,CAAC;IAEV;;OAEG;IACH,oBAAoB,CAAC,EAAE,CACrB,SAAS,EAAE,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,KAClD,IAAI,CAAC;IAEV;;OAEG;IACH,wBAAwB,CAAC,EAAE,CACzB,SAAS,EAAE,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,KAClD,IAAI,CAAC;IAEV;;OAEG;IACH,yBAAyB,CAAC,EAAE,CAC1B,SAAS,EAAE,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,KAClD,IAAI,CAAC;IAEV;;;;;;;;;SASK;IACL,iBAAiB,CAAC,EAAE,CAClB,QAAQ,EAAE,WAAW,CAAC,MAAM,CAAC,EAC7B,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,EAChC,gBAAgB,EAAE,gBAAgB,KAC/B,KAAK,CAAC,SAAS,CAAC;IAErB;;;;;;;;;SASK;IACL,kBAAkB,CAAC,EAAE,CACnB,QAAQ,EAAE,WAAW,CAAC,MAAM,CAAC,EAC7B,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,EAChC,gBAAgB,EAAE,gBAAgB,KAC/B,KAAK,CAAC,SAAS,CAAC;IAErB,gBAAgB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE3C;;;OAGG;IACH,cAAc,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAEtC;;;OAGG;IACH,sBAAsB,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAE9C;;;OAGG;IACH,+BAA+B,CAAC,EAAE,gBAAgB,CAAC;IAEnD;;;OAGG;IACH,4BAA4B,CAAC,EAAE,gBAAgB,CAAC;IAEhD;;;OAGG;IACH,qBAAqB,CAAC,EAAE,gBAAgB,CAAC;CAC1C;AAED,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB,QAAQ,EAAE,MAAM,IAAI,CAAC;IACrB,SAAS,EAAE,MAAM,IAAI,CAAC;IACtB,KAAK,EAAE,MAAM,IAAI,CAAC;CACnB;AAED,QAAA,MAAM,SAAS,yFA+iBd,CAAC;AAEF,eAAe,SAAS,CAAC;AACzB,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,gBAAgB,CAAC,CAAC"}