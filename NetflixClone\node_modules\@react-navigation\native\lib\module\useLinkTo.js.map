{"version": 3, "names": ["NavigationContainerRefContext", "React", "useLinkBuilder", "useLinkTo", "navigation", "useContext", "buildAction", "linkTo", "useCallback", "href", "undefined", "Error", "action", "dispatch"], "sourceRoot": "../../src", "sources": ["useLinkTo.tsx"], "mappings": ";;AAAA,SAASA,6BAA6B,QAAQ,wBAAwB;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAc,QAAQ,qBAAkB;;AAEjD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,MAAMC,UAAU,GAAGH,KAAK,CAACI,UAAU,CAACL,6BAA6B,CAAC;EAClE,MAAM;IAAEM;EAAY,CAAC,GAAGJ,cAAc,CAAC,CAAC;EAExC,MAAMK,MAAM,GAAGN,KAAK,CAACO,WAAW,CAC7BC,IAAY,IAAK;IAChB,IAAIL,UAAU,KAAKM,SAAS,EAAE;MAC5B,MAAM,IAAIC,KAAK,CACb,kFACF,CAAC;IACH;IAEA,MAAMC,MAAM,GAAGN,WAAW,CAACG,IAAI,CAAC;IAEhCL,UAAU,CAACS,QAAQ,CAACD,MAAM,CAAC;EAC7B,CAAC,EACD,CAACN,WAAW,EAAEF,UAAU,CAC1B,CAAC;EAED,OAAOG,MAAM;AACf", "ignoreList": []}