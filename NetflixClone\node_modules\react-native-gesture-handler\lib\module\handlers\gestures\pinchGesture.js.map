{"version": 3, "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "scaleChange", "scale", "PinchGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/pinchGesture.ts"], "mappings": ";;AAAA,SAASA,oBAAoB,QAAQ,WAAW;AAQhD,SAASC,qBAAqBA,CAC5BC,OAA4D,EAC5DC,QAA8D,EAC9D;EACA,SAAS;;EACT,IAAIC,aAA6C;EACjD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,WAAW,EAAEJ,OAAO,CAACK;IACvB,CAAC;EACH,CAAC,MAAM;IACLH,aAAa,GAAG;MACdE,WAAW,EAAEJ,OAAO,CAACK,KAAK,GAAGJ,QAAQ,CAACI;IACxC,CAAC;EACH;EAEA,OAAO;IAAE,GAAGL,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;AAEA,OAAO,MAAMI,YAAY,SAASR,oBAAoB,CAGpD;EACAS,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,qBAAqB;EAC1C;EAEAC,QAAQA,CACNC,QAIS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAACZ,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF", "ignoreList": []}