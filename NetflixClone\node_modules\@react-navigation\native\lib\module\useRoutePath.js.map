{"version": 3, "names": ["getPathFromState", "useStateForPath", "React", "LinkingContext", "useRoutePath", "options", "useContext", "state", "undefined", "Error", "getPathFromStateHelper", "path", "useMemo", "enabled", "config"], "sourceRoot": "../../src", "sources": ["useRoutePath.tsx"], "mappings": ";;AAAA,SAASA,gBAAgB,EAAEC,eAAe,QAAQ,wBAAwB;AAC1E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAc,QAAQ,qBAAkB;;AAEjD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC7B,MAAM;IAAEC;EAAQ,CAAC,GAAGH,KAAK,CAACI,UAAU,CAACH,cAAc,CAAC;EACpD,MAAMI,KAAK,GAAGN,eAAe,CAAC,CAAC;EAE/B,IAAIM,KAAK,KAAKC,SAAS,EAAE;IACvB,MAAM,IAAIC,KAAK,CACb,+FACF,CAAC;EACH;EAEA,MAAMC,sBAAsB,GAAGL,OAAO,EAAEL,gBAAgB,IAAIA,gBAAgB;EAE5E,MAAMW,IAAI,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM;IAC/B,IAAIP,OAAO,EAAEQ,OAAO,KAAK,KAAK,EAAE;MAC9B,OAAOL,SAAS;IAClB;IAEA,MAAMG,IAAI,GAAGD,sBAAsB,CAACH,KAAK,EAAEF,OAAO,EAAES,MAAM,CAAC;IAE3D,OAAOH,IAAI;EACb,CAAC,EAAE,CAACN,OAAO,EAAEQ,OAAO,EAAER,OAAO,EAAES,MAAM,EAAEP,KAAK,EAAEG,sBAAsB,CAAC,CAAC;EAEtE,OAAOC,IAAI;AACb", "ignoreList": []}