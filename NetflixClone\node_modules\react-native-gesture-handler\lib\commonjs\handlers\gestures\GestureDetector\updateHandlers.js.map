{"version": 3, "names": ["_handlersRegistry", "require", "_RNGestureHandlerModule", "_interopRequireDefault", "_utils", "_ghQueueMicrotask", "_utils2", "e", "__esModule", "default", "updateHandlers", "preparedGesture", "gestureConfig", "newGestures", "prepare", "i", "length", "handler", "attachedGestures", "checkGestureCallbacksForWorklets", "handlerTag", "handlers", "ghQueueMicrotask", "isMounted", "shouldUpdateSharedValueIfUsed", "gestureId", "shouldUseReanimated", "config", "RNGestureHandlerModule", "updateGestureHandler", "filterConfig", "ALLOWED_PROPS", "extractGestureRelations", "registerHandler", "testId", "animatedHandlers", "newHandlersValue", "filter", "g", "map", "value", "scheduleFlushOperations"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/updateHandlers.ts"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,iBAAA,GAAAJ,OAAA;AAEA,IAAAK,OAAA,GAAAL,OAAA;AAIiB,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEV,SAASG,cAAcA,CAC5BC,eAAqC,EACrCC,aAA4C,EAC5CC,WAA0B,EAC1B;EACAD,aAAa,CAACE,OAAO,CAAC,CAAC;EAEvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAME,OAAO,GAAGN,eAAe,CAACO,gBAAgB,CAACH,CAAC,CAAC;IACnD,IAAAI,wCAAgC,EAACF,OAAO,CAAC;;IAEzC;IACA;IACA,IAAIJ,WAAW,CAACE,CAAC,CAAC,CAACK,UAAU,KAAKH,OAAO,CAACG,UAAU,EAAE;MACpDP,WAAW,CAACE,CAAC,CAAC,CAACK,UAAU,GAAGH,OAAO,CAACG,UAAU;MAC9CP,WAAW,CAACE,CAAC,CAAC,CAACM,QAAQ,CAACD,UAAU,GAAGH,OAAO,CAACG,UAAU;IACzD;EACF;;EAEA;EACA,MAAMF,gBAAgB,GAAGP,eAAe,CAACO,gBAAgB;;EAEzD;EACA;EACA;EACA,IAAAI,kCAAgB,EAAC,MAAM;IACrB,IAAI,CAACX,eAAe,CAACY,SAAS,EAAE;MAC9B;IACF;;IAEA;IACA,IAAIL,gBAAgB,KAAKP,eAAe,CAACO,gBAAgB,EAAE;MACzD;IACF;;IAEA;IACA,IAAIM,6BAA6B,GAC/BN,gBAAgB,CAACF,MAAM,KAAKH,WAAW,CAACG,MAAM;IAEhD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,MAAME,OAAO,GAAGC,gBAAgB,CAACH,CAAC,CAAC;;MAEnC;MACA;MACA,IACEE,OAAO,CAACI,QAAQ,CAACI,SAAS,KAAKZ,WAAW,CAACE,CAAC,CAAC,CAACM,QAAQ,CAACI,SAAS,KAC/DZ,WAAW,CAACE,CAAC,CAAC,CAACW,mBAAmB,IAAIT,OAAO,CAACS,mBAAmB,CAAC,EACnE;QACAF,6BAA6B,GAAG,IAAI;MACtC;MAEAP,OAAO,CAACU,MAAM,GAAGd,WAAW,CAACE,CAAC,CAAC,CAACY,MAAM;MACtCV,OAAO,CAACI,QAAQ,GAAGR,WAAW,CAACE,CAAC,CAAC,CAACM,QAAQ;MAE1CO,+BAAsB,CAACC,oBAAoB,CACzCZ,OAAO,CAACG,UAAU,EAClB,IAAAU,mBAAY,EACVb,OAAO,CAACU,MAAM,EACdI,qBAAa,EACb,IAAAC,+BAAuB,EAACf,OAAO,CACjC,CACF,CAAC;MAED,IAAAgB,iCAAe,EAAChB,OAAO,CAACG,UAAU,EAAEH,OAAO,EAAEA,OAAO,CAACU,MAAM,CAACO,MAAM,CAAC;IACrE;IAEA,IAAIvB,eAAe,CAACwB,gBAAgB,IAAIX,6BAA6B,EAAE;MACrE,MAAMY,gBAAgB,GAAGlB,gBAAgB,CACtCmB,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACZ,mBAAmB,CAAC,CAAC;MAAA,CACrCa,GAAG,CAAED,CAAC,IAAKA,CAAC,CAACjB,QAAQ,CAErB;MAEHV,eAAe,CAACwB,gBAAgB,CAACK,KAAK,GAAGJ,gBAAgB;IAC3D;IAEA,IAAAK,8BAAuB,EAAC,CAAC;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}