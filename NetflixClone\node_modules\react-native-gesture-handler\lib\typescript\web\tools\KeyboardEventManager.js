import { EventTypes } from '../interfaces';
import EventManager from './EventManager';
import { PointerType } from '../../PointerType';
export default class KeyboardEventManager extends EventManager {
    activationKeys = ['Enter', ' '];
    cancelationKeys = ['Tab'];
    isPressed = false;
    keyDownCallback = (event) => {
        if (this.cancelationKeys.indexOf(event.key) !== -1 && this.isPressed) {
            this.dispatchEvent(event, EventTypes.CANCEL);
            return;
        }
        if (this.activationKeys.indexOf(event.key) === -1) {
            return;
        }
        this.dispatchEvent(event, EventTypes.DOWN);
    };
    keyUpCallback = (event) => {
        if (this.activationKeys.indexOf(event.key) === -1 || !this.isPressed) {
            return;
        }
        this.dispatchEvent(event, EventTypes.UP);
    };
    dispatchEvent(event, eventType) {
        if (!(event.target instanceof HTMLElement)) {
            return;
        }
        const adaptedEvent = this.mapEvent(event, eventType);
        switch (eventType) {
            case EventTypes.UP:
                this.isPressed = false;
                this.onPointerUp(adaptedEvent);
                break;
            case EventTypes.DOWN:
                this.isPressed = true;
                this.onPointerDown(adaptedEvent);
                break;
            case EventTypes.CANCEL:
                this.isPressed = false;
                this.onPointerCancel(adaptedEvent);
                break;
        }
    }
    registerListeners() {
        this.view.addEventListener('keydown', this.keyDownCallback);
        this.view.addEventListener('keyup', this.keyUpCallback);
    }
    unregisterListeners() {
        this.view.removeEventListener('keydown', this.keyDownCallback);
        this.view.removeEventListener('keyup', this.keyUpCallback);
    }
    mapEvent(event, eventType) {
        const viewRect = event.target.getBoundingClientRect();
        const viewportPosition = {
            x: viewRect?.x + viewRect?.width / 2,
            y: viewRect?.y + viewRect?.height / 2,
        };
        const relativePosition = {
            x: viewRect?.width / 2,
            y: viewRect?.height / 2,
        };
        return {
            x: viewportPosition.x,
            y: viewportPosition.y,
            offsetX: relativePosition.x,
            offsetY: relativePosition.y,
            pointerId: 0,
            eventType: eventType,
            pointerType: PointerType.KEY,
            time: event.timeStamp,
        };
    }
}
