{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_utils", "_utils2", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Wrap", "exports", "forwardRef", "children", "ref", "child", "React", "Children", "only", "isRNSVGNode", "clone", "cloneElement", "props", "jsx", "style", "display", "Error", "tagMessage", "AnimatedWrap"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/Wrap.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAAiD,IAAAG,WAAA,GAAAH,OAAA;AAAA,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE1C,MAAMkB,IAAI,GAAAC,OAAA,CAAAD,IAAA,gBAAG,IAAAE,iBAAU,EAC5B,CAAC;EAAEC;AAAS,CAAC,EAAEC,GAAG,KAAK;EACrB,IAAI;IACF;IACA,MAAMC,KAAU,GAAGC,cAAK,CAACC,QAAQ,CAACC,IAAI,CAACL,QAAQ,CAAC;IAEhD,IAAI,IAAAM,mBAAW,EAACJ,KAAK,CAAC,EAAE;MACtB,MAAMK,KAAK,gBAAGJ,cAAK,CAACK,YAAY,CAC9BN,KAAK,EACL;QAAED;MAAI,CAAC;MACP;MACAC,KAAK,CAACO,KAAK,CAACT,QACd,CAAC;MAED,OAAOO,KAAK;IACd;IAEA,oBACE,IAAA9B,WAAA,CAAAiC,GAAA;MACET,GAAG,EAAEA,GAAiC;MACtCU,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAW,CAAE;MAAAZ,QAAA,EAC9BE;IAAK,CACH,CAAC;EAEV,CAAC,CAAC,OAAOxB,CAAC,EAAE;IACV,MAAM,IAAImC,KAAK,CACb,IAAAC,iBAAU,EACR,2KACF,CACF,CAAC;EACH;AACF,CACF,CAAC;;AAED;AACA;AACO,MAAMC,YAAY,GAAAjB,OAAA,CAAAiB,YAAA,GAAGlB,IAAI", "ignoreList": []}