{"version": 3, "names": ["_findNodeHandle", "_interopRequireDefault", "require", "_PointerEventManager", "_State", "_utils", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_KeyboardEventManager", "_WheelEventManager", "e", "__esModule", "default", "GestureHandlerWebDelegate", "isInitialized", "eventManagers", "defaultViewStyles", "userSelect", "touchAction", "init", "viewRef", "handler", "Error", "handlerTag", "<PERSON><PERSON><PERSON><PERSON>", "view", "findNodeHandle", "style", "config", "setUserSelect", "enabled", "setTouchAction", "setContextMenu", "push", "PointerEventManager", "KeyboardEventManager", "WheelEventManager", "for<PERSON>ach", "manager", "attachEventManager", "isPointerInBounds", "x", "y", "measure<PERSON>iew", "rect", "getBoundingClientRect", "pageX", "left", "pageY", "top", "width", "height", "reset", "resetManager", "tryResetCursor", "activeCursor", "state", "State", "ACTIVE", "cursor", "shouldDisableContextMenu", "enableContextMenu", "undefined", "isButtonInConfig", "MouseB<PERSON>on", "RIGHT", "addContextMenuListeners", "addEventListener", "disableContextMenu", "removeContextMenuListeners", "removeEventListener", "preventDefault", "stopPropagation", "isHandlerEnabled", "onEnabledChange", "registerListeners", "unregisterListeners", "onBegin", "onActivate", "onEnd", "onCancel", "onFail", "destroy", "_view", "value", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/GestureHandlerWebDelegate.ts"], "mappings": ";;;;;;AAAA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAGA,IAAAI,qBAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,kBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAoD,SAAAD,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAO7C,MAAMG,yBAAyB,CAEtC;EACUC,aAAa,GAAG,KAAK;EAIrBC,aAAa,GAA4B,EAAE;EAC3CC,iBAAiB,GAAsB;IAC7CC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EAEDC,IAAIA,CAACC,OAAe,EAAEC,OAAwB,EAAQ;IACpD,IAAI,CAACD,OAAO,EAAE;MACZ,MAAM,IAAIE,KAAK,CACb,wCAAwCD,OAAO,CAACE,UAAU,EAC5D,CAAC;IACH;IAEA,IAAI,CAACT,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACU,cAAc,GAAGH,OAAO;IAC7B,IAAI,CAACI,IAAI,GAAG,IAAAC,uBAAc,EAACN,OAAO,CAA2B;IAE7D,IAAI,CAACJ,iBAAiB,GAAG;MACvBC,UAAU,EAAE,IAAI,CAACQ,IAAI,CAACE,KAAK,CAACV,UAAU;MACtCC,WAAW,EAAE,IAAI,CAACO,IAAI,CAACE,KAAK,CAACT;IAC/B,CAAC;IAED,MAAMU,MAAM,GAAGP,OAAO,CAACO,MAAM;IAE7B,IAAI,CAACC,aAAa,CAACD,MAAM,CAACE,OAAO,CAAC;IAClC,IAAI,CAACC,cAAc,CAACH,MAAM,CAACE,OAAO,CAAC;IACnC,IAAI,CAACE,cAAc,CAACJ,MAAM,CAACE,OAAO,CAAC;IAEnC,IAAI,CAACf,aAAa,CAACkB,IAAI,CAAC,IAAIC,4BAAmB,CAAC,IAAI,CAACT,IAAI,CAAC,CAAC;IAC3D,IAAI,CAACV,aAAa,CAACkB,IAAI,CAAC,IAAIE,6BAAoB,CAAC,IAAI,CAACV,IAAI,CAAC,CAAC;IAC5D,IAAI,CAACV,aAAa,CAACkB,IAAI,CAAC,IAAIG,0BAAiB,CAAC,IAAI,CAACX,IAAI,CAAC,CAAC;IAEzD,IAAI,CAACV,aAAa,CAACsB,OAAO,CAAEC,OAAO,IACjC,IAAI,CAACd,cAAc,CAACe,kBAAkB,CAACD,OAAO,CAChD,CAAC;EACH;EAEAE,iBAAiBA,CAAC;IAAEC,CAAC;IAAEC;EAA4B,CAAC,EAAW;IAC7D,OAAO,IAAAF,wBAAiB,EAAC,IAAI,CAACf,IAAI,EAAE;MAAEgB,CAAC;MAAEC;IAAE,CAAC,CAAC;EAC/C;EAEAC,WAAWA,CAAA,EAAkB;IAC3B,MAAMC,IAAI,GAAG,IAAI,CAACnB,IAAI,CAACoB,qBAAqB,CAAC,CAAC;IAE9C,OAAO;MACLC,KAAK,EAAEF,IAAI,CAACG,IAAI;MAChBC,KAAK,EAAEJ,IAAI,CAACK,GAAG;MACfC,KAAK,EAAEN,IAAI,CAACM,KAAK;MACjBC,MAAM,EAAEP,IAAI,CAACO;IACf,CAAC;EACH;EAEAC,KAAKA,CAAA,EAAS;IACZ,IAAI,CAACrC,aAAa,CAACsB,OAAO,CAAEC,OAA8B,IACxDA,OAAO,CAACe,YAAY,CAAC,CACvB,CAAC;EACH;EAEAC,cAAcA,CAAA,EAAG;IACf,MAAM1B,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACI,MAAM;IAEzC,IACEA,MAAM,CAAC2B,YAAY,IACnB3B,MAAM,CAAC2B,YAAY,KAAK,MAAM,IAC9B,IAAI,CAAC/B,cAAc,CAACgC,KAAK,KAAKC,YAAK,CAACC,MAAM,EAC1C;MACA,IAAI,CAACjC,IAAI,CAACE,KAAK,CAACgC,MAAM,GAAG,MAAM;IACjC;EACF;EAEQC,wBAAwBA,CAAChC,MAAc,EAAE;IAC/C,OACGA,MAAM,CAACiC,iBAAiB,KAAKC,SAAS,IACrC,IAAI,CAACtC,cAAc,CAACuC,gBAAgB,CAACC,iCAAW,CAACC,KAAK,CAAC,IACzDrC,MAAM,CAACiC,iBAAiB,KAAK,KAAK;EAEtC;EAEQK,uBAAuBA,CAACtC,MAAc,EAAQ;IACpD,IAAI,IAAI,CAACgC,wBAAwB,CAAChC,MAAM,CAAC,EAAE;MACzC,IAAI,CAACH,IAAI,CAAC0C,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACC,kBAAkB,CAAC;IACpE,CAAC,MAAM,IAAIxC,MAAM,CAACiC,iBAAiB,EAAE;MACnC,IAAI,CAACpC,IAAI,CAAC0C,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACN,iBAAiB,CAAC;IACnE;EACF;EAEQQ,0BAA0BA,CAACzC,MAAc,EAAQ;IACvD,IAAI,IAAI,CAACgC,wBAAwB,CAAChC,MAAM,CAAC,EAAE;MACzC,IAAI,CAACH,IAAI,CAAC6C,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACF,kBAAkB,CAAC;IACvE,CAAC,MAAM,IAAIxC,MAAM,CAACiC,iBAAiB,EAAE;MACnC,IAAI,CAACpC,IAAI,CAAC6C,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACT,iBAAiB,CAAC;IACtE;EACF;EAEQO,kBAAkBA,CAAa1D,CAAa,EAAQ;IAC1DA,CAAC,CAAC6D,cAAc,CAAC,CAAC;EACpB;EAEQV,iBAAiBA,CAAanD,CAAa,EAAQ;IACzDA,CAAC,CAAC8D,eAAe,CAAC,CAAC;EACrB;EAEQ3C,aAAaA,CAAC4C,gBAAyB,EAAE;IAC/C,MAAM;MAAExD;IAAW,CAAC,GAAG,IAAI,CAACO,cAAc,CAACI,MAAM;IAEjD,IAAI,CAACH,IAAI,CAACE,KAAK,CAAC,YAAY,CAAC,GAAG8C,gBAAgB,GAC3CxD,UAAU,IAAI,MAAM,GACrB,IAAI,CAACD,iBAAiB,CAACC,UAAU;IAErC,IAAI,CAACQ,IAAI,CAACE,KAAK,CAAC,kBAAkB,CAAC,GAAG8C,gBAAgB,GACjDxD,UAAU,IAAI,MAAM,GACrB,IAAI,CAACD,iBAAiB,CAACC,UAAU;EACvC;EAEQc,cAAcA,CAAC0C,gBAAyB,EAAE;IAChD,MAAM;MAAEvD;IAAY,CAAC,GAAG,IAAI,CAACM,cAAc,CAACI,MAAM;IAElD,IAAI,CAACH,IAAI,CAACE,KAAK,CAAC,aAAa,CAAC,GAAG8C,gBAAgB,GAC5CvD,WAAW,IAAI,MAAM,GACtB,IAAI,CAACF,iBAAiB,CAACE,WAAW;;IAEtC;IACA,IAAI,CAACO,IAAI,CAACE,KAAK,CAAC,oBAAoB,CAAC,GAAG8C,gBAAgB,GACnDvD,WAAW,IAAI,MAAM,GACtB,IAAI,CAACF,iBAAiB,CAACE,WAAW;EACxC;EAEQc,cAAcA,CAACyC,gBAAyB,EAAE;IAChD,MAAM7C,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACI,MAAM;IAEzC,IAAI6C,gBAAgB,EAAE;MACpB,IAAI,CAACP,uBAAuB,CAACtC,MAAM,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACyC,0BAA0B,CAACzC,MAAM,CAAC;IACzC;EACF;EAEA8C,eAAeA,CAAC5C,OAAgB,EAAQ;IACtC,IAAI,CAAC,IAAI,CAAChB,aAAa,EAAE;MACvB;IACF;IAEA,IAAI,CAACe,aAAa,CAACC,OAAO,CAAC;IAC3B,IAAI,CAACC,cAAc,CAACD,OAAO,CAAC;IAC5B,IAAI,CAACE,cAAc,CAACF,OAAO,CAAC;IAE5B,IAAIA,OAAO,EAAE;MACX,IAAI,CAACf,aAAa,CAACsB,OAAO,CAAEC,OAAO,IAAK;QACtC;QACA;QACA;QACA;QACA;QACAA,OAAO,CAACqC,iBAAiB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC5D,aAAa,CAACsB,OAAO,CAAEC,OAAO,IAAK;QACtCA,OAAO,CAACsC,mBAAmB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF;EAEAC,OAAOA,CAAA,EAAS;IACd;EAAA;EAGFC,UAAUA,CAAA,EAAS;IACjB,MAAMlD,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACI,MAAM;IAEzC,IACE,CAAC,CAAC,IAAI,CAACH,IAAI,CAACE,KAAK,CAACgC,MAAM,IAAI,IAAI,CAAClC,IAAI,CAACE,KAAK,CAACgC,MAAM,KAAK,MAAM,KAC7D/B,MAAM,CAAC2B,YAAY,EACnB;MACA,IAAI,CAAC9B,IAAI,CAACE,KAAK,CAACgC,MAAM,GAAG/B,MAAM,CAAC2B,YAAY;IAC9C;EACF;EAEAwB,KAAKA,CAAA,EAAS;IACZ,IAAI,CAACzB,cAAc,CAAC,CAAC;EACvB;EAEA0B,QAAQA,CAAA,EAAS;IACf,IAAI,CAAC1B,cAAc,CAAC,CAAC;EACvB;EAEA2B,MAAMA,CAAA,EAAS;IACb,IAAI,CAAC3B,cAAc,CAAC,CAAC;EACvB;EAEO4B,OAAOA,CAACtD,MAAc,EAAQ;IACnC,IAAI,CAACyC,0BAA0B,CAACzC,MAAM,CAAC;IAEvC,IAAI,CAACb,aAAa,CAACsB,OAAO,CAAEC,OAAO,IAAK;MACtCA,OAAO,CAACsC,mBAAmB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA,IAAWnD,IAAIA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC0D,KAAK;EACnB;EACA,IAAW1D,IAAIA,CAAC2D,KAAkB,EAAE;IAClC,IAAI,CAACD,KAAK,GAAGC,KAAK;EACpB;AACF;AAACC,OAAA,CAAAxE,yBAAA,GAAAA,yBAAA", "ignoreList": []}