{"version": 3, "names": ["React", "PanGestureHandler", "PanGestureHandlerNative", "GestureHandlerRefContext", "jsx", "_jsx", "props", "gestureRef", "useRef", "Provider", "value", "children", "ref", "GestureHandlerRootView", "State", "GestureState"], "sourceRoot": "../../../src", "sources": ["views/GestureHandlerNative.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,iBAAiB,IAAIC,uBAAuB,QAEvC,8BAA8B;AAErC,SAASC,wBAAwB,QAAQ,sCAAmC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE7E,OAAO,SAASJ,iBAAiBA,CAACK,KAAkC,EAAE;EACpE,MAAMC,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAA0B,IAAI,CAAC;EAE9D,oBACEH,IAAA,CAACF,wBAAwB,CAACM,QAAQ;IAACC,KAAK,EAAEH,UAAW;IAAAI,QAAA,eACnDN,IAAA,CAACH,uBAAuB;MAAA,GAAKI,KAAK;MAAEM,GAAG,EAAEL;IAAW,CAAE;EAAC,CACtB,CAAC;AAExC;AAGA,SACEM,sBAAsB,EACtBC,KAAK,IAAIC,YAAY,QAChB,8BAA8B", "ignoreList": []}