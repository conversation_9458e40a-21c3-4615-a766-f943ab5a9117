{"version": 3, "names": ["_gesture", "require", "extendRelation", "currentRelation", "extendWith", "undefined", "ComposedGesture", "Gesture", "gestures", "simultaneousGestures", "requireGesturesToFail", "constructor", "prepareSingleGesture", "gesture", "BaseGesture", "newConfig", "config", "simultaneousWith", "requireToFail", "prepare", "initialize", "toGestureArray", "flatMap", "exports", "SimultaneousGesture", "simultaneousArrays", "map", "filter", "x", "i", "length", "ExclusiveGesture", "gestureArrays", "concat"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureComposition.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,SAASC,cAAcA,CACrBC,eAAyC,EACzCC,UAAyB,EACzB;EACA,IAAID,eAAe,KAAKE,SAAS,EAAE;IACjC,OAAO,CAAC,GAAGD,UAAU,CAAC;EACxB,CAAC,MAAM;IACL,OAAO,CAAC,GAAGD,eAAe,EAAE,GAAGC,UAAU,CAAC;EAC5C;AACF;AAEO,MAAME,eAAe,SAASC,gBAAO,CAAC;EACjCC,QAAQ,GAAc,EAAE;EACxBC,oBAAoB,GAAkB,EAAE;EACxCC,qBAAqB,GAAkB,EAAE;EAEnDC,WAAWA,CAAC,GAAGH,QAAmB,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEUI,oBAAoBA,CAC5BC,OAAgB,EAChBJ,oBAAmC,EACnCC,qBAAoC,EACpC;IACA,IAAIG,OAAO,YAAYC,oBAAW,EAAE;MAClC,MAAMC,SAAS,GAAG;QAAE,GAAGF,OAAO,CAACG;MAAO,CAAC;;MAEvC;MACA;MACAD,SAAS,CAACE,gBAAgB,GAAGf,cAAc,CACzCa,SAAS,CAACE,gBAAgB,EAC1BR,oBACF,CAAC;MACDM,SAAS,CAACG,aAAa,GAAGhB,cAAc,CACtCa,SAAS,CAACG,aAAa,EACvBR,qBACF,CAAC;MAEDG,OAAO,CAACG,MAAM,GAAGD,SAAS;IAC5B,CAAC,MAAM,IAAIF,OAAO,YAAYP,eAAe,EAAE;MAC7CO,OAAO,CAACJ,oBAAoB,GAAGA,oBAAoB;MACnDI,OAAO,CAACH,qBAAqB,GAAGA,qBAAqB;MACrDG,OAAO,CAACM,OAAO,CAAC,CAAC;IACnB;EACF;EAEAA,OAAOA,CAAA,EAAG;IACR,KAAK,MAAMN,OAAO,IAAI,IAAI,CAACL,QAAQ,EAAE;MACnC,IAAI,CAACI,oBAAoB,CACvBC,OAAO,EACP,IAAI,CAACJ,oBAAoB,EACzB,IAAI,CAACC,qBACP,CAAC;IACH;EACF;EAEAU,UAAUA,CAAA,EAAG;IACX,KAAK,MAAMP,OAAO,IAAI,IAAI,CAACL,QAAQ,EAAE;MACnCK,OAAO,CAACO,UAAU,CAAC,CAAC;IACtB;EACF;EAEAC,cAAcA,CAAA,EAAkB;IAC9B,OAAO,IAAI,CAACb,QAAQ,CAACc,OAAO,CAAET,OAAO,IAAKA,OAAO,CAACQ,cAAc,CAAC,CAAC,CAAC;EACrE;AACF;AAACE,OAAA,CAAAjB,eAAA,GAAAA,eAAA;AAEM,MAAMkB,mBAAmB,SAASlB,eAAe,CAAC;EACvDa,OAAOA,CAAA,EAAG;IACR;IACA;IACA,MAAMM,kBAAkB,GAAG,IAAI,CAACjB,QAAQ,CAACkB,GAAG,CAAEb,OAAO;IACnD;IACA,IAAI,CAACL;IACH;IAAA,CACCmB,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKf,OAAO;IAC5B;IACA;IACA;IACA;IAAA,CACCS,OAAO,CAAEM,CAAC,IAAKA,CAAC,CAACP,cAAc,CAAC,CAAC,CACtC,CAAC;IAED,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrB,QAAQ,CAACsB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACjB,oBAAoB,CACvB,IAAI,CAACJ,QAAQ,CAACqB,CAAC,CAAC,EAChBJ,kBAAkB,CAACI,CAAC,CAAC,EACrB,IAAI,CAACnB,qBACP,CAAC;IACH;EACF;AACF;AAACa,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAEM,MAAMO,gBAAgB,SAASzB,eAAe,CAAC;EACpDa,OAAOA,CAAA,EAAG;IACR;IACA;IACA,MAAMa,aAAa,GAAG,IAAI,CAACxB,QAAQ,CAACkB,GAAG,CAAEb,OAAO,IAC9CA,OAAO,CAACQ,cAAc,CAAC,CACzB,CAAC;IAED,IAAIH,aAA4B,GAAG,EAAE;IAErC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrB,QAAQ,CAACsB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACjB,oBAAoB,CACvB,IAAI,CAACJ,QAAQ,CAACqB,CAAC,CAAC,EAChB,IAAI,CAACpB,oBAAoB,EACzB,IAAI,CAACC,qBAAqB,CAACuB,MAAM,CAACf,aAAa,CACjD,CAAC;;MAED;MACAA,aAAa,GAAGA,aAAa,CAACe,MAAM,CAACD,aAAa,CAACH,CAAC,CAAC,CAAC;IACxD;EACF;AACF;AAACN,OAAA,CAAAQ,gBAAA,GAAAA,gBAAA", "ignoreList": []}