{"version": 3, "names": ["React", "Platform", "UIManager", "DeviceEventEmitter", "customDirectEventTypes", "RNGestureHandlerModule", "State", "handlerIDToTag", "registerOldGestureHandler", "unregisterOldGestureHandler", "getNextHandlerTag", "filterConfig", "scheduleFlushOperations", "findNodeHandle", "deepEqual", "isF<PERSON><PERSON>", "isReact19", "isTestEnv", "tagMessage", "ActionType", "PressabilityDebugView", "GestureHandlerRootViewContext", "ghQueueMicrotask", "MountRegistry", "jsx", "_jsx", "UIManagerAny", "topGestureHandlerEvent", "registrationName", "customGHEventsConfigFabricAndroid", "topOnGestureHandlerEvent", "topOnGestureHandlerStateChange", "customGHEventsConfig", "onGestureHandlerEvent", "onGestureHandlerStateChange", "OS", "genericDirectEventTypes", "UIManagerConstants", "getViewManagerConfig", "setJSResponder", "oldSetJSResponder", "clearJSResponder", "oldClearJSResponder", "tag", "blockNativeResponder", "handleSetJSResponder", "handleClearJSResponder", "allowTouches", "DEV_ON_ANDROID", "__DEV__", "addListener", "hasUnresolvedRefs", "props", "extract", "refs", "Array", "isArray", "current", "some", "r", "stateToPropMappings", "UNDETERMINED", "undefined", "BEGAN", "FAILED", "CANCELLED", "ACTIVE", "END", "UNRESOLVED_REFS_RETRY_LIMIT", "createHandler", "name", "allowedProps", "config", "transformProps", "customNativeProps", "Handler", "Component", "displayName", "contextType", "handlerTag", "constructor", "propsRef", "createRef", "isMountedRef", "state", "id", "Error", "componentDidMount", "inspectorToggleListener", "setState", "_", "update", "createGestureHandler", "viewNode", "attachGestureHandler", "componentDidUpdate", "viewTag", "componentWillUnmount", "remove", "dropGestureHandler", "handlerID", "gestureHandlerWillUnmount", "event", "nativeEvent", "onGestureEvent", "onHandlerStateChange", "stateEventName", "<PERSON><PERSON><PERSON><PERSON>", "ref<PERSON><PERSON><PERSON>", "node", "child", "Children", "only", "children", "ref", "newConfig", "newViewTag", "JS_FUNCTION_OLD_API", "onGestureStateChange", "actionType", "isGestureHandlerWorklet", "isStateChangeHandlerWorklet", "is<PERSON><PERSON><PERSON>", "REANIMATED_WORKLET", "NATIVE_ANIMATED_EVENT", "gestureHandlerWillMount", "updateGestureHandler", "remainingTries", "setNativeProps", "updates", "mergedProps", "render", "context", "gestureEventHandler", "gestureStateEventHandler", "events", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "toArray", "push", "color", "hitSlop", "cloneElement", "collapsable", "handlerType", "enabled", "testID"], "sourceRoot": "../../../src", "sources": ["handlers/createHandler.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,SAAS,EACTC,kBAAkB,QAEb,cAAc;AACrB,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,SAASC,KAAK,QAAQ,UAAU;AAChC,SACEC,cAAc,EACdC,yBAAyB,EACzBC,2BAA2B,QACtB,oBAAoB;AAC3B,SAASC,iBAAiB,QAAQ,qBAAqB;AAOvD,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,SAAS;AAC/D,OAAOC,cAAc,MAAM,mBAAmB;AAE9C,SACEC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,UAAU,QACL,UAAU;AACjB,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,OAAOC,6BAA6B,MAAM,kCAAkC;AAC5E,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAGjD,MAAMC,YAAY,GAAGxB,SAAgB;AAErCE,sBAAsB,CAACuB,sBAAsB,GAAG;EAC9CC,gBAAgB,EAAE;AACpB,CAAC;AAED,MAAMC,iCAAiC,GAAG;EACxCC,wBAAwB,EAAE;IAAEF,gBAAgB,EAAE;EAAwB,CAAC;EACvEG,8BAA8B,EAAE;IAC9BH,gBAAgB,EAAE;EACpB;AACF,CAAC;AAED,MAAMI,oBAAoB,GAAG;EAC3BC,qBAAqB,EAAE;IAAEL,gBAAgB,EAAE;EAAwB,CAAC;EACpEM,2BAA2B,EAAE;IAC3BN,gBAAgB,EAAE;EACpB,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIb,QAAQ,CAAC,CAAC,IACZd,QAAQ,CAACkC,EAAE,KAAK,SAAS,IACzBN,iCAAiC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACAH,YAAY,CAACU,uBAAuB,GAAG;EACrC,GAAGV,YAAY,CAACU,uBAAuB;EACvC,GAAGJ;AACL,CAAC;AAED,MAAMK,kBAAkB,GAAGX,YAAY,CAACY,oBAAoB,GAAG,cAAc,CAAC;AAE9E,IAAID,kBAAkB,EAAE;EACtBA,kBAAkB,CAACD,uBAAuB,GAAG;IAC3C,GAAGC,kBAAkB,CAACD,uBAAuB;IAC7C,GAAGJ;EACL,CAAC;AACH;;AAEA;AACA,MAAM;EACJO,cAAc,EAAEC,iBAAiB,GAAGA,CAAA,KAAM;IACxC;EAAA,CACD;EACDC,gBAAgB,EAAEC,mBAAmB,GAAGA,CAAA,KAAM;IAC5C;EAAA;AAEJ,CAAC,GAAGhB,YAAY;AAChBA,YAAY,CAACa,cAAc,GAAG,CAACI,GAAW,EAAEC,oBAA6B,KAAK;EAC5EvC,sBAAsB,CAACwC,oBAAoB,CAACF,GAAG,EAAEC,oBAAoB,CAAC;EACtEJ,iBAAiB,CAACG,GAAG,EAAEC,oBAAoB,CAAC;AAC9C,CAAC;AACDlB,YAAY,CAACe,gBAAgB,GAAG,MAAM;EACpCpC,sBAAsB,CAACyC,sBAAsB,CAAC,CAAC;EAC/CJ,mBAAmB,CAAC,CAAC;AACvB,CAAC;AAED,IAAIK,YAAY,GAAG,IAAI;AACvB,MAAMC,cAAc,GAAGC,OAAO,IAAIhD,QAAQ,CAACkC,EAAE,KAAK,SAAS;AAC3D;AACA;AACA,IAAIa,cAAc,EAAE;EAClB7C,kBAAkB,CAAC+C,WAAW,CAAC,wBAAwB,EAAE,MAAM;IAC7DH,YAAY,GAAG,CAACA,YAAY;EAC9B,CAAC,CAAC;AACJ;AAKA,SAASI,iBAAiBA,CACxBC,KAAsB,EACtB;EACA;EACA,MAAMC,OAAO,GAAIC,IAAiB,IAAK;IACrC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACxB,OAAOA,IAAI,IAAIA,IAAI,CAACG,OAAO,KAAK,IAAI;IACtC;IACA,OAAOH,IAAI,CAACI,IAAI,CAAEC,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACF,OAAO,KAAK,IAAI,CAAC;EAClD,CAAC;EACD,OAAOJ,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAC,CAAC,IAAIC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC,CAAC;AAC5E;AAEA,MAAMQ,mBAAmB,GAAG;EAC1B,CAACtD,KAAK,CAACuD,YAAY,GAAGC,SAAS;EAC/B,CAACxD,KAAK,CAACyD,KAAK,GAAG,SAAS;EACxB,CAACzD,KAAK,CAAC0D,MAAM,GAAG,UAAU;EAC1B,CAAC1D,KAAK,CAAC2D,SAAS,GAAG,aAAa;EAChC,CAAC3D,KAAK,CAAC4D,MAAM,GAAG,aAAa;EAC7B,CAAC5D,KAAK,CAAC6D,GAAG,GAAG;AACf,CAAU;;AAWV;;AAcA,MAAMC,2BAA2B,GAAG,CAAC;;AAErC;AACA,eAAe,SAASC,aAAaA,CAGnC;EACAC,IAAI;EACJC,YAAY,GAAG,EAAE;EACjBC,MAAM,GAAG,CAAC,CAAC;EACXC,cAAc;EACdC,iBAAiB,GAAG;AACA,CAAC,EAAqD;EAI1E,MAAMC,OAAO,SAAS3E,KAAK,CAAC4E,SAAS,CAGnC;IACA,OAAOC,WAAW,GAAGP,IAAI;IACzB,OAAOQ,WAAW,GAAGzD,6BAA6B;IAE1C0D,UAAU,GAAG,CAAC,CAAC;IAQvBC,WAAWA,CAAC5B,KAAgC,EAAE;MAC5C,KAAK,CAACA,KAAK,CAAC;MACZ,IAAI,CAACoB,MAAM,GAAG,CAAC,CAAC;MAChB,IAAI,CAACS,QAAQ,gBAAGjF,KAAK,CAACkF,SAAS,CAAC,CAAC;MACjC,IAAI,CAACC,YAAY,gBAAGnF,KAAK,CAACkF,SAAS,CAAC,CAAC;MACrC,IAAI,CAACE,KAAK,GAAG;QAAErC;MAAa,CAAC;MAC7B,IAAIK,KAAK,CAACiC,EAAE,EAAE;QACZ,IAAI9E,cAAc,CAAC6C,KAAK,CAACiC,EAAE,CAAC,KAAKvB,SAAS,EAAE;UAC1C,MAAM,IAAIwB,KAAK,CAAC,oBAAoBlC,KAAK,CAACiC,EAAE,sBAAsB,CAAC;QACrE;QACA9E,cAAc,CAAC6C,KAAK,CAACiC,EAAE,CAAC,GAAG,IAAI,CAACN,UAAU;MAC5C;IACF;IAEAQ,iBAAiBA,CAAA,EAAG;MAClB,MAAMnC,KAAsB,GAAG,IAAI,CAACA,KAAK;MACzC,IAAI,CAAC+B,YAAY,CAAC1B,OAAO,GAAG,IAAI;MAEhC,IAAIT,cAAc,EAAE;QAClB,IAAI,CAACwC,uBAAuB,GAAGrF,kBAAkB,CAAC+C,WAAW,CAC3D,wBAAwB,EACxB,MAAM;UACJ,IAAI,CAACuC,QAAQ,CAAEC,CAAC,KAAM;YAAE3C;UAAa,CAAC,CAAC,CAAC;UACxC,IAAI,CAAC4C,MAAM,CAACvB,2BAA2B,CAAC;QAC1C,CACF,CAAC;MACH;MACA,IAAIjB,iBAAiB,CAACC,KAAK,CAAC,EAAE;QAC5B;QACA;QACA;QACA;QACA;QACA;QACA9B,gBAAgB,CAAC,MAAM;UACrB,IAAI,CAACqE,MAAM,CAACvB,2BAA2B,CAAC;QAC1C,CAAC,CAAC;MACJ;MAEA,IAAI,CAACwB,oBAAoB,CACvBjF,YAAY,CACV8D,cAAc,GAAGA,cAAc,CAAC,IAAI,CAACrB,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,EACxD,CAAC,GAAGmB,YAAY,EAAE,GAAGG,iBAAiB,CAAC,EACvCF,MACF,CACF,CAAC;MAED,IAAI,CAAC,IAAI,CAACqB,QAAQ,EAAE;QAClB,MAAM,IAAIP,KAAK,CACb,+CAA+CX,OAAO,CAACE,WAAW,4DACpE,CAAC;MACH;MAEA,IAAI,CAACiB,oBAAoB,CAACjF,cAAc,CAAC,IAAI,CAACgF,QAAQ,CAAW,CAAC,CAAC,CAAC;IACtE;IAEAE,kBAAkBA,CAAA,EAAG;MACnB,MAAMC,OAAO,GAAGnF,cAAc,CAAC,IAAI,CAACgF,QAAQ,CAAC;MAC7C,IAAI,IAAI,CAACG,OAAO,KAAKA,OAAO,EAAE;QAC5B,IAAI,CAACF,oBAAoB,CAACE,OAAiB,CAAC,CAAC,CAAC;MAChD;MACA,IAAI,CAACL,MAAM,CAACvB,2BAA2B,CAAC;IAC1C;IAEA6B,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACT,uBAAuB,EAAEU,MAAM,CAAC,CAAC;MACtC,IAAI,CAACf,YAAY,CAAC1B,OAAO,GAAG,KAAK;MACjC,IAAIxD,QAAQ,CAACkC,EAAE,KAAK,KAAK,EAAE;QACzB1B,2BAA2B,CAAC,IAAI,CAACsE,UAAU,CAAC;MAC9C;MACA1E,sBAAsB,CAAC8F,kBAAkB,CAAC,IAAI,CAACpB,UAAU,CAAC;MAC1DnE,uBAAuB,CAAC,CAAC;MACzB;MACA,MAAMwF,SAA6B,GAAG,IAAI,CAAChD,KAAK,CAACiC,EAAE;MACnD,IAAIe,SAAS,EAAE;QACb;QACA,OAAO7F,cAAc,CAAC6F,SAAS,CAAC;MAClC;MAEA7E,aAAa,CAAC8E,yBAAyB,CAAC,IAAI,CAAC;IAC/C;IAEQpE,qBAAqB,GAAIqE,KAAsB,IAAK;MAC1D,IAAIA,KAAK,CAACC,WAAW,CAACxB,UAAU,KAAK,IAAI,CAACA,UAAU,EAAE;QACpD,IAAI,OAAO,IAAI,CAAC3B,KAAK,CAACoD,cAAc,KAAK,UAAU,EAAE;UACnD,IAAI,CAACpD,KAAK,CAACoD,cAAc,GAAGF,KAAK,CAAC;QACpC;MACF,CAAC,MAAM;QACL,IAAI,CAAClD,KAAK,CAACnB,qBAAqB,GAAGqE,KAAK,CAAC;MAC3C;IACF,CAAC;;IAED;IACQpE,2BAA2B,GACjCoE,KAAiC,IAC9B;MACH,IAAIA,KAAK,CAACC,WAAW,CAACxB,UAAU,KAAK,IAAI,CAACA,UAAU,EAAE;QACpD,IAAI,OAAO,IAAI,CAAC3B,KAAK,CAACqD,oBAAoB,KAAK,UAAU,EAAE;UACzD,IAAI,CAACrD,KAAK,CAACqD,oBAAoB,GAAGH,KAAK,CAAC;QAC1C;QAEA,MAAMlB,KAA4B,GAAGkB,KAAK,CAACC,WAAW,CAACnB,KAAK;QAC5D,MAAMsB,cAAc,GAAG9C,mBAAmB,CAACwB,KAAK,CAAC;QACjD,MAAMuB,YAAY,GAAGD,cAAc,IAAI,IAAI,CAACtD,KAAK,CAACsD,cAAc,CAAC;QACjE,IAAIC,YAAY,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;UACtDA,YAAY,CAACL,KAAK,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAI,CAAClD,KAAK,CAAClB,2BAA2B,GAAGoE,KAAK,CAAC;MACjD;IACF,CAAC;IAEOM,UAAU,GAAIC,IAAS,IAAK;MAClC,IAAI,CAAChB,QAAQ,GAAGgB,IAAI;MAEpB,MAAMC,KAAK,GAAG9G,KAAK,CAAC+G,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC5D,KAAK,CAAC6D,QAAQ,CAAC;MACtD;MACA;MACA,MAAMC,GAAG,GAAGlG,SAAS,CAAC,CAAC,GAAI8F,KAAK,CAAkB1D,KAAK,EAAE8D,GAAG,GAAGJ,KAAK,EAAEI,GAAG;MAEzE,IAAI,CAACA,GAAG,EAAE;QACR;MACF;MAEA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;QAC7BA,GAAG,CAACL,IAAI,CAAC;MACX,CAAC,MAAM;QACLK,GAAG,CAACzD,OAAO,GAAGoD,IAAI;MACpB;IACF,CAAC;IAEOjB,oBAAoB,GAC1BuB,SAA4C,IACzC;MACH,IAAI,CAACpC,UAAU,GAAGrE,iBAAiB,CAAC,CAAC;MACrC,IAAI,CAAC8D,MAAM,GAAG2C,SAAS;MAEvB9G,sBAAsB,CAACuF,oBAAoB,CACzCtB,IAAI,EACJ,IAAI,CAACS,UAAU,EACfoC,SACF,CAAC;IACH,CAAC;IAEOrB,oBAAoB,GAAIsB,UAAkB,IAAK;MACrD,IAAI,CAACpB,OAAO,GAAGoB,UAAU;MAEzB,IAAInH,QAAQ,CAACkC,EAAE,KAAK,KAAK,EAAE;QACzB;QAEE9B,sBAAsB,CAACyF,oBAAoB,CAE3C,IAAI,CAACf,UAAU,EACfqC,UAAU,EACVjG,UAAU,CAACkG,mBAAmB;QAAE;QAChC,IAAI,CAACpC,QACP,CAAC;MACH,CAAC,MAAM;QACLzE,yBAAyB,CAAC,IAAI,CAACuE,UAAU,EAAE;UACzCyB,cAAc,EAAE,IAAI,CAACvE,qBAAqB;UAC1CqF,oBAAoB,EAAE,IAAI,CAACpF;QAC7B,CAAC,CAAC;QAEF,MAAMqF,UAAU,GAAG,CAAC,MAAM;UACxB,MAAMf,cAAc,GAAG,IAAI,CAACpD,KAAK,EAAEoD,cAAc;UACjD,MAAMgB,uBAAuB,GAC3BhB,cAAc,KACb,SAAS,IAAIA,cAAc,IAC1B,qBAAqB,IAAIA,cAAc,CAAC;UAC5C,MAAMC,oBAAoB,GAAG,IAAI,CAACrD,KAAK,EAAEqD,oBAAoB;UAC7D,MAAMgB,2BAA2B,GAC/BhB,oBAAoB,KACnB,SAAS,IAAIA,oBAAoB,IAChC,qBAAqB,IAAIA,oBAAoB,CAAC;UAClD,MAAMiB,mBAAmB,GACvBF,uBAAuB,IAAIC,2BAA2B;UACxD,IAAIC,mBAAmB,EAAE;YACvB;YACA,OAAOvG,UAAU,CAACwG,kBAAkB;UACtC,CAAC,MAAM,IAAInB,cAAc,IAAI,YAAY,IAAIA,cAAc,EAAE;YAC3D;YACA,OAAOrF,UAAU,CAACyG,qBAAqB;UACzC,CAAC,MAAM;YACL;YACA,OAAOzG,UAAU,CAACkG,mBAAmB;UACvC;QACF,CAAC,EAAE,CAAC;QAEJhH,sBAAsB,CAACyF,oBAAoB,CACzC,IAAI,CAACf,UAAU,EACfqC,UAAU,EACVG,UACF,CAAC;MACH;MAEA3G,uBAAuB,CAAC,CAAC;MAEzBU,gBAAgB,CAAC,MAAM;QACrBC,aAAa,CAACsG,uBAAuB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC;IAEOC,oBAAoB,GAC1BX,SAA4C,IACzC;MACH,IAAI,CAAC3C,MAAM,GAAG2C,SAAS;MAEvB9G,sBAAsB,CAACyH,oBAAoB,CAAC,IAAI,CAAC/C,UAAU,EAAEoC,SAAS,CAAC;MACvEvG,uBAAuB,CAAC,CAAC;IAC3B,CAAC;IAEO+E,MAAMA,CAACoC,cAAsB,EAAE;MACrC,IAAI,CAAC,IAAI,CAAC5C,YAAY,CAAC1B,OAAO,EAAE;QAC9B;MACF;MAEA,MAAML,KAAsB,GAAG,IAAI,CAACA,KAAK;;MAEzC;MACA;MACA;MACA,IAAID,iBAAiB,CAACC,KAAK,CAAC,IAAI2E,cAAc,GAAG,CAAC,EAAE;QAClDzG,gBAAgB,CAAC,MAAM;UACrB,IAAI,CAACqE,MAAM,CAACoC,cAAc,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMZ,SAAS,GAAGxG,YAAY,CAC5B8D,cAAc,GAAGA,cAAc,CAAC,IAAI,CAACrB,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,EACxD,CAAC,GAAGmB,YAAY,EAAE,GAAGG,iBAAiB,CAAC,EACvCF,MACF,CAAC;QACD,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC0D,MAAM,EAAE2C,SAAS,CAAC,EAAE;UACtC,IAAI,CAACW,oBAAoB,CAACX,SAAS,CAAC;QACtC;MACF;IACF;;IAEA;IACAa,cAAcA,CAACC,OAAY,EAAE;MAC3B,MAAMC,WAAW,GAAG;QAAE,GAAG,IAAI,CAAC9E,KAAK;QAAE,GAAG6E;MAAQ,CAAC;MACjD,MAAMd,SAAS,GAAGxG,YAAY,CAC5B8D,cAAc,GAAGA,cAAc,CAACyD,WAAW,CAAC,GAAGA,WAAW,EAC1D,CAAC,GAAG3D,YAAY,EAAE,GAAGG,iBAAiB,CAAC,EACvCF,MACF,CAAC;MACD,IAAI,CAACsD,oBAAoB,CAACX,SAAS,CAAC;IACtC;IAEAgB,MAAMA,CAAA,EAAG;MACP,IAAIlF,OAAO,IAAI,CAAC,IAAI,CAACmF,OAAO,IAAI,CAACnH,SAAS,CAAC,CAAC,IAAIhB,QAAQ,CAACkC,EAAE,KAAK,KAAK,EAAE;QACrE,MAAM,IAAImD,KAAK,CACbhB,IAAI,GACF,yMACJ,CAAC;MACH;MAEA,IAAI+D,mBAAmB,GAAG,IAAI,CAACpG,qBAAqB;MACpD;;MAKA,MAAM;QAAEuE,cAAc;QAAEvE;MAA8C,CAAC,GACrE,IAAI,CAACmB,KAAK;MACZ,IAAIoD,cAAc,IAAI,OAAOA,cAAc,KAAK,UAAU,EAAE;QAC1D;QACA;QACA;QACA,IAAIvE,qBAAqB,EAAE;UACzB,MAAM,IAAIqD,KAAK,CACb,yEACF,CAAC;QACH;QACA+C,mBAAmB,GAAG7B,cAAc;MACtC,CAAC,MAAM;QACL,IACEvE,qBAAqB,IACrB,OAAOA,qBAAqB,KAAK,UAAU,EAC3C;UACA,MAAM,IAAIqD,KAAK,CACb,yEACF,CAAC;QACH;MACF;MAEA,IAAIgD,wBAAwB,GAAG,IAAI,CAACpG,2BAA2B;MAC/D;;MAKA,MAAM;QACJuE,oBAAoB;QACpBvE;MAC4B,CAAC,GAAG,IAAI,CAACkB,KAAK;MAC5C,IAAIqD,oBAAoB,IAAI,OAAOA,oBAAoB,KAAK,UAAU,EAAE;QACtE;QACA;QACA;QACA,IAAIvE,2BAA2B,EAAE;UAC/B,MAAM,IAAIoD,KAAK,CACb,yEACF,CAAC;QACH;QACAgD,wBAAwB,GAAG7B,oBAAoB;MACjD,CAAC,MAAM;QACL,IACEvE,2BAA2B,IAC3B,OAAOA,2BAA2B,KAAK,UAAU,EACjD;UACA,MAAM,IAAIoD,KAAK,CACb,yEACF,CAAC;QACH;MACF;MACA,MAAMiD,MAAM,GAAG;QACbtG,qBAAqB,EAAE,IAAI,CAACmD,KAAK,CAACrC,YAAY,GAC1CsF,mBAAmB,GACnBvE,SAAS;QACb5B,2BAA2B,EAAE,IAAI,CAACkD,KAAK,CAACrC,YAAY,GAChDuF,wBAAwB,GACxBxE;MACN,CAAC;MAED,IAAI,CAACmB,QAAQ,CAACxB,OAAO,GAAG8E,MAAM;MAE9B,IAAIzB,KAAU,GAAG,IAAI;MACrB,IAAI;QACFA,KAAK,GAAG9G,KAAK,CAAC+G,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC5D,KAAK,CAAC6D,QAAQ,CAAC;MAClD,CAAC,CAAC,OAAOuB,CAAC,EAAE;QACV,MAAM,IAAIlD,KAAK,CACbpE,UAAU,CACR,GAAGoD,IAAI,4JACT,CACF,CAAC;MACH;MAEA,IAAImE,aAAa,GAAG3B,KAAK,CAAC1D,KAAK,CAAC6D,QAAQ;MACxC,IACEhE,OAAO,IACP6D,KAAK,CAAC4B,IAAI,KACT5B,KAAK,CAAC4B,IAAI,KAAK,wBAAwB,IACtC5B,KAAK,CAAC4B,IAAI,CAACpE,IAAI,KAAK,MAAM,IAC1BwC,KAAK,CAAC4B,IAAI,CAAC7D,WAAW,KAAK,MAAM,CAAC,EACpC;QACA4D,aAAa,GAAGzI,KAAK,CAAC+G,QAAQ,CAAC4B,OAAO,CAACF,aAAa,CAAC;QACrDA,aAAa,CAACG,IAAI,cAChBnH,IAAA,CAACL,qBAAqB;UAEpByH,KAAK,EAAC,mBAAmB;UACzBC,OAAO,EAAEhC,KAAK,CAAC1D,KAAK,CAAC0F;QAAQ,GAFzB,uBAGL,CACH,CAAC;MACH;MAEA,oBAAO9I,KAAK,CAAC+I,YAAY,CACvBjC,KAAK,EACL;QACEI,GAAG,EAAE,IAAI,CAACN,UAAU;QACpBoC,WAAW,EAAE,KAAK;QAClB,IAAI/H,SAAS,CAAC,CAAC,GACX;UACEgI,WAAW,EAAE3E,IAAI;UACjBS,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BmE,OAAO,EAAE,IAAI,CAAC9F,KAAK,CAAC8F;QACtB,CAAC,GACD,CAAC,CAAC,CAAC;QACPC,MAAM,EAAE,IAAI,CAAC/F,KAAK,CAAC+F,MAAM,IAAIrC,KAAK,CAAC1D,KAAK,CAAC+F,MAAM;QAC/C,GAAGZ;MACL,CAAC,EACDE,aACF,CAAC;IACH;EACF;EACA,OAAO9D,OAAO;AAChB", "ignoreList": []}