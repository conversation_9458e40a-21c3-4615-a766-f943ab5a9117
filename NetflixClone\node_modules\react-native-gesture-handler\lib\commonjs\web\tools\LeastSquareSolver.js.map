{"version": 3, "names": ["Vector", "constructor", "length", "offset", "elements", "Array", "fromVOL", "values", "result", "get", "index", "set", "value", "dot", "other", "i", "norm", "Math", "sqrt", "Matrix", "rows", "columns", "row", "column", "getRow", "PolynomialFit", "degree", "coefficients", "precisionErrorTolerance", "LeastSquareSolver", "x", "y", "w", "solve", "m", "n", "a", "h", "q", "r", "j", "inverseNorm", "wy", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["web/tools/LeastSquareSolver.ts"], "mappings": ";;;;;;AAAA;AACA;;AAEA,MAAMA,MAAM,CAAC;EAKXC,WAAWA,CAACC,MAAc,EAAE;IAC1B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,QAAQ,GAAG,IAAIC,KAAK,CAASH,MAAM,CAAC;EAC3C;EAEA,OAAcI,OAAOA,CACnBC,MAAgB,EAChBJ,MAAc,EACdD,MAAc,EACN;IACR,MAAMM,MAAM,GAAG,IAAIR,MAAM,CAAC,CAAC,CAAC;IAE5BQ,MAAM,CAACL,MAAM,GAAGA,MAAM;IACtBK,MAAM,CAACN,MAAM,GAAGA,MAAM;IACtBM,MAAM,CAACJ,QAAQ,GAAGG,MAAM;IAExB,OAAOC,MAAM;EACf;EAEOC,GAAGA,CAACC,KAAa,EAAU;IAChC,OAAO,IAAI,CAACN,QAAQ,CAAC,IAAI,CAACD,MAAM,GAAGO,KAAK,CAAC;EAC3C;EAEOC,GAAGA,CAACD,KAAa,EAAEE,KAAa,EAAQ;IAC7C,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACD,MAAM,GAAGO,KAAK,CAAC,GAAGE,KAAK;EAC5C;EAEOC,GAAGA,CAACC,KAAa,EAAU;IAChC,IAAIN,MAAM,GAAG,CAAC;IACd,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACb,MAAM,EAAEa,CAAC,EAAE,EAAE;MACpCP,MAAM,IAAI,IAAI,CAACC,GAAG,CAACM,CAAC,CAAC,GAAGD,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC;IACtC;IACA,OAAOP,MAAM;EACf;EAEOQ,IAAIA,CAAA,EAAG;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACL,GAAG,CAAC,IAAI,CAAC,CAAC;EAClC;AACF;AAEA,MAAMM,MAAM,CAAC;EAIXlB,WAAWA,CAACmB,IAAY,EAAEC,OAAe,EAAE;IACzC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjB,QAAQ,GAAG,IAAIC,KAAK,CAASe,IAAI,GAAGC,OAAO,CAAC;EACnD;EAEOZ,GAAGA,CAACa,GAAW,EAAEC,MAAc,EAAU;IAC9C,OAAO,IAAI,CAACnB,QAAQ,CAACkB,GAAG,GAAG,IAAI,CAACD,OAAO,GAAGE,MAAM,CAAC;EACnD;EAEOZ,GAAGA,CAACW,GAAW,EAAEC,MAAc,EAAEX,KAAa,EAAQ;IAC3D,IAAI,CAACR,QAAQ,CAACkB,GAAG,GAAG,IAAI,CAACD,OAAO,GAAGE,MAAM,CAAC,GAAGX,KAAK;EACpD;EAEOY,MAAMA,CAACF,GAAW,EAAU;IACjC,OAAOtB,MAAM,CAACM,OAAO,CAAC,IAAI,CAACF,QAAQ,EAAEkB,GAAG,GAAG,IAAI,CAACD,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC;EACxE;AACF;;AAEA;AACA,MAAMI,aAAa,CAAC;EAClB;EACA;EACA;EACA;;EAGA;EACA;EACA;EACAxB,WAAWA,CAACyB,MAAc,EAAE;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAItB,KAAK,CAASqB,MAAM,GAAG,CAAC,CAAC;EACnD;AACF;AAEA,MAAME,uBAAuB,GAAG,KAAK;;AAErC;AACe,MAAMC,iBAAiB,CAAC;EACrC;;EAEA;;EAEA;;EAGA;EACA;EACA;EACA5B,WAAWA,CAAC6B,CAAW,EAAEC,CAAW,EAAEC,CAAW,EAAE;IACjD,IAAI,CAACF,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACZ;;EAEA;EACA;EACA;EACOC,KAAKA,CAACP,MAAc,EAAwB;IACjD,IAAIA,MAAM,GAAG,IAAI,CAACI,CAAC,CAAC5B,MAAM,EAAE;MAC1B;MACA,OAAO,IAAI;IACb;IAEA,MAAMM,MAAM,GAAG,IAAIiB,aAAa,CAACC,MAAM,CAAC;;IAExC;IACA,MAAMQ,CAAC,GAAG,IAAI,CAACJ,CAAC,CAAC5B,MAAM;IACvB,MAAMiC,CAAC,GAAGT,MAAM,GAAG,CAAC;;IAEpB;IACA,MAAMU,CAAC,GAAG,IAAIjB,MAAM,CAACgB,CAAC,EAAED,CAAC,CAAC;IAC1B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1BD,CAAC,CAACzB,GAAG,CAAC,CAAC,EAAE0B,CAAC,EAAE,IAAI,CAACL,CAAC,CAACK,CAAC,CAAC,CAAC;MAEtB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,CAAC,EAAEpB,CAAC,EAAE,EAAE;QAC1BqB,CAAC,CAACzB,GAAG,CAACI,CAAC,EAAEsB,CAAC,EAAED,CAAC,CAAC3B,GAAG,CAACM,CAAC,GAAG,CAAC,EAAEsB,CAAC,CAAC,GAAG,IAAI,CAACP,CAAC,CAACO,CAAC,CAAC,CAAC;MAC1C;IACF;;IAEA;;IAEA;IACA,MAAMC,CAAC,GAAG,IAAInB,MAAM,CAACgB,CAAC,EAAED,CAAC,CAAC;IAC1B;IACA,MAAMK,CAAC,GAAG,IAAIpB,MAAM,CAACgB,CAAC,EAAED,CAAC,CAAC;IAE1B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAE;MAC7B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE;QAC7BC,CAAC,CAAC3B,GAAG,CAAC6B,CAAC,EAAEH,CAAC,EAAED,CAAC,CAAC3B,GAAG,CAAC+B,CAAC,EAAEH,CAAC,CAAC,CAAC;MAC1B;MACA,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,IAAI,CAAC,EAAE;QAC7B,MAAMF,GAAG,GAAGyB,CAAC,CAACd,MAAM,CAACgB,CAAC,CAAC,CAAC3B,GAAG,CAACyB,CAAC,CAACd,MAAM,CAACT,CAAC,CAAC,CAAC;QACxC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE;UAC7BC,CAAC,CAAC3B,GAAG,CAAC6B,CAAC,EAAEH,CAAC,EAAEC,CAAC,CAAC7B,GAAG,CAAC+B,CAAC,EAAEH,CAAC,CAAC,GAAGxB,GAAG,GAAGyB,CAAC,CAAC7B,GAAG,CAACM,CAAC,EAAEsB,CAAC,CAAC,CAAC;QAC9C;MACF;MAEA,MAAMrB,IAAI,GAAGsB,CAAC,CAACd,MAAM,CAACgB,CAAC,CAAC,CAACxB,IAAI,CAAC,CAAC;MAC/B,IAAIA,IAAI,GAAGY,uBAAuB,EAAE;QAClC;QACA,OAAO,IAAI;MACb;MAEA,MAAMa,WAAW,GAAG,GAAG,GAAGzB,IAAI;MAC9B,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE;QAC7BC,CAAC,CAAC3B,GAAG,CAAC6B,CAAC,EAAEH,CAAC,EAAEC,CAAC,CAAC7B,GAAG,CAAC+B,CAAC,EAAEH,CAAC,CAAC,GAAGI,WAAW,CAAC;MACxC;MACA,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,CAAC,EAAEpB,CAAC,IAAI,CAAC,EAAE;QAC7BwB,CAAC,CAAC5B,GAAG,CAAC6B,CAAC,EAAEzB,CAAC,EAAEA,CAAC,GAAGyB,CAAC,GAAG,GAAG,GAAGF,CAAC,CAACd,MAAM,CAACgB,CAAC,CAAC,CAAC3B,GAAG,CAACuB,CAAC,CAACZ,MAAM,CAACT,CAAC,CAAC,CAAC,CAAC;MACzD;IACF;;IAEA;IACA;IACA,MAAM2B,EAAE,GAAG,IAAI1C,MAAM,CAACkC,CAAC,CAAC;IACxB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE;MAC7BK,EAAE,CAAC/B,GAAG,CAAC0B,CAAC,EAAE,IAAI,CAACN,CAAC,CAACM,CAAC,CAAC,GAAG,IAAI,CAACL,CAAC,CAACK,CAAC,CAAC,CAAC;IAClC;IACA,KAAK,IAAItB,CAAC,GAAGoB,CAAC,GAAG,CAAC,EAAEpB,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAClCP,MAAM,CAACmB,YAAY,CAACZ,CAAC,CAAC,GAAGuB,CAAC,CAACd,MAAM,CAACT,CAAC,CAAC,CAACF,GAAG,CAAC6B,EAAE,CAAC;MAC5C,KAAK,IAAIF,CAAC,GAAGL,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGzB,CAAC,EAAEyB,CAAC,IAAI,CAAC,EAAE;QACjChC,MAAM,CAACmB,YAAY,CAACZ,CAAC,CAAC,IAAIwB,CAAC,CAAC9B,GAAG,CAACM,CAAC,EAAEyB,CAAC,CAAC,GAAGhC,MAAM,CAACmB,YAAY,CAACa,CAAC,CAAC;MAChE;MACAhC,MAAM,CAACmB,YAAY,CAACZ,CAAC,CAAC,IAAIwB,CAAC,CAAC9B,GAAG,CAACM,CAAC,EAAEA,CAAC,CAAC;IACvC;IAEA,OAAOP,MAAM;EACf;AACF;AAACmC,OAAA,CAAAC,OAAA,GAAAf,iBAAA", "ignoreList": []}