{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reanimated<PERSON><PERSON>per", "_utils", "e", "__esModule", "default", "Wrap", "React", "Component", "render", "child", "Children", "only", "props", "children", "cloneElement", "collapsable", "Error", "tagMessage", "exports", "AnimatedWrap", "Reanimated", "createAnimatedComponent"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/Wrap.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAA4C,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAErC,MAAMG,IAAI,SAASC,cAAK,CAACC,SAAS,CAItC;EACDC,MAAMA,CAAA,EAAG;IACP,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAMC,KAAU,GAAGH,cAAK,CAACI,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC;MAC3D,oBAAOP,cAAK,CAACQ,YAAY,CACvBL,KAAK,EACL;QAAEM,WAAW,EAAE;MAAM,CAAC;MACtB;MACAN,KAAK,CAACG,KAAK,CAACC,QACd,CAAC;IACH,CAAC,CAAC,OAAOX,CAAC,EAAE;MACV,MAAM,IAAIc,KAAK,CACb,IAAAC,iBAAU,EACR,2KACF,CACF,CAAC;IACH;EACF;AACF;AAACC,OAAA,CAAAb,IAAA,GAAAA,IAAA;AAEM,MAAMc,YAAY,GAAAD,OAAA,CAAAC,YAAA,GACvBC,6BAAU,EAAEhB,OAAO,EAAEiB,uBAAuB,CAAChB,IAAI,CAAC,IAAIA,IAAI", "ignoreList": []}