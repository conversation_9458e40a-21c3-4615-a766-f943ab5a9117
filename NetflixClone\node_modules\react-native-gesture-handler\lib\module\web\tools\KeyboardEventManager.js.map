{"version": 3, "names": ["EventTypes", "EventManager", "PointerType", "KeyboardEventManager", "activationKeys", "cancelationKeys", "isPressed", "keyDownCallback", "event", "indexOf", "key", "dispatchEvent", "CANCEL", "DOWN", "keyUp<PERSON><PERSON><PERSON>", "UP", "eventType", "target", "HTMLElement", "adaptedEvent", "mapEvent", "onPointerUp", "onPointerDown", "onPointerCancel", "registerListeners", "view", "addEventListener", "unregisterListeners", "removeEventListener", "viewRect", "getBoundingClientRect", "viewportPosition", "x", "width", "y", "height", "relativePosition", "offsetX", "offsetY", "pointerId", "pointerType", "KEY", "time", "timeStamp"], "sourceRoot": "../../../../src", "sources": ["web/tools/KeyboardEventManager.ts"], "mappings": ";;AAAA,SAAuBA,UAAU,QAAQ,eAAe;AACxD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,eAAe,MAAMC,oBAAoB,SAASF,YAAY,CAAc;EAClEG,cAAc,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC;EAC/BC,eAAe,GAAG,CAAC,KAAK,CAAC;EACzBC,SAAS,GAAG,KAAK;EAEjBC,eAAe,GAAIC,KAAoB,IAAW;IACxD,IAAI,IAAI,CAACH,eAAe,CAACI,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAACJ,SAAS,EAAE;MACpE,IAAI,CAACK,aAAa,CAACH,KAAK,EAAER,UAAU,CAACY,MAAM,CAAC;MAC5C;IACF;IAEA,IAAI,IAAI,CAACR,cAAc,CAACK,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjD;IACF;IAEA,IAAI,CAACC,aAAa,CAACH,KAAK,EAAER,UAAU,CAACa,IAAI,CAAC;EAC5C,CAAC;EAEOC,aAAa,GAAIN,KAAoB,IAAW;IACtD,IAAI,IAAI,CAACJ,cAAc,CAACK,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACpE;IACF;IAEA,IAAI,CAACK,aAAa,CAACH,KAAK,EAAER,UAAU,CAACe,EAAE,CAAC;EAC1C,CAAC;EAEOJ,aAAaA,CAACH,KAAoB,EAAEQ,SAAqB,EAAE;IACjE,IAAI,EAAER,KAAK,CAACS,MAAM,YAAYC,WAAW,CAAC,EAAE;MAC1C;IACF;IAEA,MAAMC,YAAY,GAAG,IAAI,CAACC,QAAQ,CAACZ,KAAK,EAAEQ,SAAS,CAAC;IAEpD,QAAQA,SAAS;MACf,KAAKhB,UAAU,CAACe,EAAE;QAChB,IAAI,CAACT,SAAS,GAAG,KAAK;QACtB,IAAI,CAACe,WAAW,CAACF,YAAY,CAAC;QAC9B;MACF,KAAKnB,UAAU,CAACa,IAAI;QAClB,IAAI,CAACP,SAAS,GAAG,IAAI;QACrB,IAAI,CAACgB,aAAa,CAACH,YAAY,CAAC;QAChC;MACF,KAAKnB,UAAU,CAACY,MAAM;QACpB,IAAI,CAACN,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiB,eAAe,CAACJ,YAAY,CAAC;QAClC;IACJ;EACF;EAEOK,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACnB,eAAe,CAAC;IAC3D,IAAI,CAACkB,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACZ,aAAa,CAAC;EACzD;EAEOa,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACF,IAAI,CAACG,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACrB,eAAe,CAAC;IAC9D,IAAI,CAACkB,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACd,aAAa,CAAC;EAC5D;EAEUM,QAAQA,CAChBZ,KAAoB,EACpBQ,SAAqB,EACP;IACd,MAAMa,QAAQ,GAAIrB,KAAK,CAACS,MAAM,CAAiBa,qBAAqB,CAAC,CAAC;IAEtE,MAAMC,gBAAgB,GAAG;MACvBC,CAAC,EAAEH,QAAQ,EAAEG,CAAC,GAAGH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACpCC,CAAC,EAAEL,QAAQ,EAAEK,CAAC,GAAGL,QAAQ,EAAEM,MAAM,GAAG;IACtC,CAAC;IAED,MAAMC,gBAAgB,GAAG;MACvBJ,CAAC,EAAEH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACtBC,CAAC,EAAEL,QAAQ,EAAEM,MAAM,GAAG;IACxB,CAAC;IAED,OAAO;MACLH,CAAC,EAAED,gBAAgB,CAACC,CAAC;MACrBE,CAAC,EAAEH,gBAAgB,CAACG,CAAC;MACrBG,OAAO,EAAED,gBAAgB,CAACJ,CAAC;MAC3BM,OAAO,EAAEF,gBAAgB,CAACF,CAAC;MAC3BK,SAAS,EAAE,CAAC;MACZvB,SAAS,EAAEA,SAAS;MACpBwB,WAAW,EAAEtC,WAAW,CAACuC,GAAG;MAC5BC,IAAI,EAAElC,KAAK,CAACmC;IACd,CAAC;EACH;AACF", "ignoreList": []}