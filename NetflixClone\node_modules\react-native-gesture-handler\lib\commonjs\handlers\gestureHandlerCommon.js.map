{"version": 3, "names": ["commonProps", "componentInteractionProps", "baseGestureHandlerProps", "exports", "baseGestureHandlerWithDetectorProps", "MouseB<PERSON>on"], "sourceRoot": "../../../src", "sources": ["handlers/gestureHandlerCommon.ts"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;AAQA,MAAMA,WAAW,GAAG,CAClB,IAAI,EACJ,SAAS,EACT,yBAAyB,EACzB,SAAS,EACT,sBAAsB,EACtB,YAAY,EACZ,cAAc,EACd,aAAa,EACb,mBAAmB,EACnB,aAAa,CACL;AAEV,MAAMC,yBAAyB,GAAG,CAChC,SAAS,EACT,sBAAsB,EACtB,gBAAgB,CACR;AAEH,MAAMC,uBAAuB,GAAAC,OAAA,CAAAD,uBAAA,GAAG,CACrC,GAAGF,WAAW,EACd,GAAGC,yBAAyB,EAC5B,SAAS,EACT,UAAU,EACV,aAAa,EACb,aAAa,EACb,SAAS,EACT,gBAAgB,EAChB,sBAAsB,CACd;AAEH,MAAMG,mCAAmC,GAAAD,OAAA,CAAAC,mCAAA,GAAG,CACjD,GAAGJ,WAAW,EACd,kBAAkB,EAClB,kBAAkB,CACnB;AAAC,IAkEUK,WAAW,GAAAF,OAAA,CAAAE,WAAA,0BAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA,OA0BvB;AA+CA;AACA", "ignoreList": []}