{"version": 3, "names": ["_handlersRegistry", "require", "_RNGestureHandlerModule", "_interopRequireDefault", "_utils", "_mountRegistry", "e", "__esModule", "default", "dropHandlers", "preparedGesture", "handler", "attachedGestures", "RNGestureHandlerModule", "dropGestureHandler", "handlerTag", "unregister<PERSON><PERSON><PERSON>", "config", "testId", "MountRegistry", "gestureWillUnmount", "scheduleFlushOperations"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/dropHandlers.ts"], "mappings": ";;;;;;AAAA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,cAAA,GAAAJ,OAAA;AAAuD,SAAAE,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhD,SAASG,YAAYA,CAACC,eAAqC,EAAE;EAClE,KAAK,MAAMC,OAAO,IAAID,eAAe,CAACE,gBAAgB,EAAE;IACtDC,+BAAsB,CAACC,kBAAkB,CAACH,OAAO,CAACI,UAAU,CAAC;IAE7D,IAAAC,mCAAiB,EAACL,OAAO,CAACI,UAAU,EAAEJ,OAAO,CAACM,MAAM,CAACC,MAAM,CAAC;IAE5DC,4BAAa,CAACC,kBAAkB,CAACT,OAAO,CAAC;EAC3C;EAEA,IAAAU,8BAAuB,EAAC,CAAC;AAC3B", "ignoreList": []}