{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_gestureObjects", "_GestureDetector", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Text", "exports", "forwardRef", "props", "ref", "onPress", "onLongPress", "rest", "textRef", "useRef", "native", "Gesture", "Native", "runOnJS", "ref<PERSON><PERSON><PERSON>", "node", "current", "rngh", "useEffect", "Platform", "OS", "textElement", "setAttribute", "jsx", "GestureDetector", "gesture", "children"], "sourceRoot": "../../../src", "sources": ["components/Text.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAOA,IAAAC,YAAA,GAAAD,OAAA;AAMA,IAAAE,eAAA,GAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAH,OAAA;AAAuE,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEhE,MAAMkB,IAAI,GAAAC,OAAA,CAAAD,IAAA,gBAAG,IAAAE,iBAAU,EAC5B,CACEC,KAAkB,EAClBC,GAAoD,KACjD;EACH,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAE,GAAGC;EAAK,CAAC,GAAGJ,KAAK;EAE/C,MAAMK,OAAO,GAAG,IAAAC,aAAM,EAAgB,IAAI,CAAC;EAC3C,MAAMC,MAAM,GAAGC,8BAAO,CAACC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC;EAE7C,MAAMC,UAAU,GAAIC,IAAS,IAAK;IAChCP,OAAO,CAACQ,OAAO,GAAGD,IAAI;IAEtB,IAAIX,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;MAC7BA,GAAG,CAACW,IAAI,CAAC;IACX,CAAC,MAAM;MACLX,GAAG,CAACY,OAAO,GAAGD,IAAI;IACpB;EACF,CAAC;;EAED;EACA;EACA;EACAD,UAAU,CAACG,IAAI,GAAG,IAAI;EAEtB,IAAAC,gBAAS,EAAC,MAAM;IACd,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACzB;IACF;IAEA,MAAMC,WAAW,GAAGjB,GAAG,GAClBA,GAAG,CAAkDY,OAAO,GAC7DR,OAAO,CAACQ,OAAO;;IAEnB;IACCK,WAAW,EAAgCC,YAAY,CACtD,UAAU,EACV,MACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOjB,OAAO,IAAIC,WAAW,gBAC3B,IAAA1B,WAAA,CAAA2C,GAAA,EAAC5C,gBAAA,CAAA6C,eAAe;IAACC,OAAO,EAAEf,MAAO;IAAAgB,QAAA,eAC/B,IAAA9C,WAAA,CAAA2C,GAAA,EAAC9C,YAAA,CAAAuB,IAAM;MACLK,OAAO,EAAEA,OAAQ;MACjBC,WAAW,EAAEA,WAAY;MACzBF,GAAG,EAAEU,UAAW;MAAA,GACZP;IAAI,CACT;EAAC,CACa,CAAC,gBAElB,IAAA3B,WAAA,CAAA2C,GAAA,EAAC9C,YAAA,CAAAuB,IAAM;IAACI,GAAG,EAAEA,GAAI;IAAA,GAAKG;EAAI,CAAG,CAC9B;AACH,CACF,CAAC;AACD", "ignoreList": []}