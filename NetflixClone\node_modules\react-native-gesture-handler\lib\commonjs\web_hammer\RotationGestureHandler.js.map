{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_constants", "_IndiscreteGestureHandler", "e", "__esModule", "default", "RotationGestureHandler", "IndiscreteGestureHandler", "name", "NativeGestureClass", "Hammer", "Rotate", "transformNativeEvent", "rotation", "velocity", "center", "initialRotation", "DEG_RAD", "anchorX", "x", "anchorY", "y", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/RotationGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AAEA,IAAAE,yBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAkE,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElE,MAAMG,sBAAsB,SAASC,iCAAwB,CAAC;EAC5D,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,QAAQ;EACjB;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOC,iBAAM,CAACC,MAAM;EACtB;EAEAC,oBAAoBA,CAAC;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAuB,CAAC,EAAE;IACnE,OAAO;MACLF,QAAQ,EAAE,CAACA,QAAQ,IAAI,IAAI,CAACG,eAAe,IAAI,CAAC,CAAC,IAAIC,kBAAO;MAC5DC,OAAO,EAAEH,MAAM,CAACI,CAAC;MACjBC,OAAO,EAAEL,MAAM,CAACM,CAAC;MACjBP;IACF,CAAC;EACH;AACF;AAAC,IAAAQ,QAAA,GAAAC,OAAA,CAAAlB,OAAA,GACcC,sBAAsB", "ignoreList": []}