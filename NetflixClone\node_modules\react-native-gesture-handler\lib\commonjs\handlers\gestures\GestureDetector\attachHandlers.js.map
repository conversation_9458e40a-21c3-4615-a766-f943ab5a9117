{"version": 3, "names": ["_handlersRegistry", "require", "_RNGestureHandlerModule", "_interopRequireDefault", "_utils", "_ActionType", "_reactNative", "_ghQueueMicrotask", "_utils2", "_mountRegistry", "e", "__esModule", "default", "attachHandlers", "preparedGesture", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "viewTag", "webEventHandlersRef", "initialize", "ghQueueMicrotask", "isMounted", "prepare", "handler", "checkGestureCallbacksForWorklets", "RNGestureHandlerModule", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "filterConfig", "config", "ALLOWED_PROPS", "registerHandler", "testId", "updateGestureHandler", "extractGestureRelations", "scheduleFlushOperations", "gesture", "actionType", "shouldUseReanimated", "ActionType", "REANIMATED_WORKLET", "JS_FUNCTION_NEW_API", "Platform", "OS", "attachGestureHandler", "JS_FUNCTION_OLD_API", "MountRegistry", "gestureWillMount", "attachedGestures", "animatedHandlers", "isAnimatedGesture", "g", "value", "filter", "map", "handlers"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/attachHandlers.ts"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAL,OAAA;AAEA,IAAAM,iBAAA,GAAAN,OAAA;AAEA,IAAAO,OAAA,GAAAP,OAAA;AAKA,IAAAQ,cAAA,GAAAR,OAAA;AAAuD,SAAAE,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAUhD,SAASG,cAAcA,CAAC;EAC7BC,eAAe;EACfC,aAAa;EACbC,gBAAgB;EAChBC,OAAO;EACPC;AACoB,CAAC,EAAE;EACvBH,aAAa,CAACI,UAAU,CAAC,CAAC;;EAE1B;EACA;EACA,IAAAC,kCAAgB,EAAC,MAAM;IACrB,IAAI,CAACN,eAAe,CAACO,SAAS,EAAE;MAC9B;IACF;IACAN,aAAa,CAACO,OAAO,CAAC,CAAC;EACzB,CAAC,CAAC;EAEF,KAAK,MAAMC,OAAO,IAAIP,gBAAgB,EAAE;IACtC,IAAAQ,wCAAgC,EAACD,OAAO,CAAC;IACzCE,+BAAsB,CAACC,oBAAoB,CACzCH,OAAO,CAACI,WAAW,EACnBJ,OAAO,CAACK,UAAU,EAClB,IAAAC,mBAAY,EAACN,OAAO,CAACO,MAAM,EAAEC,qBAAa,CAC5C,CAAC;IAED,IAAAC,iCAAe,EAACT,OAAO,CAACK,UAAU,EAAEL,OAAO,EAAEA,OAAO,CAACO,MAAM,CAACG,MAAM,CAAC;EACrE;;EAEA;EACA;EACA,IAAAb,kCAAgB,EAAC,MAAM;IACrB,IAAI,CAACN,eAAe,CAACO,SAAS,EAAE;MAC9B;IACF;IACA,KAAK,MAAME,OAAO,IAAIP,gBAAgB,EAAE;MACtCS,+BAAsB,CAACS,oBAAoB,CACzCX,OAAO,CAACK,UAAU,EAClB,IAAAC,mBAAY,EACVN,OAAO,CAACO,MAAM,EACdC,qBAAa,EACb,IAAAI,+BAAuB,EAACZ,OAAO,CACjC,CACF,CAAC;IACH;IAEA,IAAAa,8BAAuB,EAAC,CAAC;EAC3B,CAAC,CAAC;EAEF,KAAK,MAAMC,OAAO,IAAIrB,gBAAgB,EAAE;IACtC,MAAMsB,UAAU,GAAGD,OAAO,CAACE,mBAAmB,GAC1CC,sBAAU,CAACC,kBAAkB,GAC7BD,sBAAU,CAACE,mBAAmB;IAElC,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MAEvBnB,+BAAsB,CAACoB,oBAAoB,CAE3CR,OAAO,CAACT,UAAU,EAClBX,OAAO,EACPuB,sBAAU,CAACM,mBAAmB;MAAE;MAChC5B,mBACF,CAAC;IACH,CAAC,MAAM;MACLO,+BAAsB,CAACoB,oBAAoB,CACzCR,OAAO,CAACT,UAAU,EAClBX,OAAO,EACPqB,UACF,CAAC;IACH;IAEAS,4BAAa,CAACC,gBAAgB,CAACX,OAAO,CAAC;EACzC;EAEAvB,eAAe,CAACmC,gBAAgB,GAAGjC,gBAAgB;EAEnD,IAAIF,eAAe,CAACoC,gBAAgB,EAAE;IACpC,MAAMC,iBAAiB,GAAIC,CAAc,IAAKA,CAAC,CAACb,mBAAmB;IAEnEzB,eAAe,CAACoC,gBAAgB,CAACG,KAAK,GAAGrC,gBAAgB,CACtDsC,MAAM,CAACH,iBAAiB,CAAC,CACzBI,GAAG,CAAEH,CAAC,IAAKA,CAAC,CAACI,QAAQ,CAErB;EACL;AACF", "ignoreList": []}