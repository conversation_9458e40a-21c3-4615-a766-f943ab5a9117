import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  ActivityIndicator,
  <PERSON>ert,
  BackH<PERSON>ler,
  <PERSON>dal,
  FlatList,
  ScrollView,
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import * as ScreenOrientation from 'expo-screen-orientation';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { NETFLIX_COLORS } from '../utils/constants';
import soraApi from '../services/soraApi';
import storageService from '../services/storage';

const { width, height } = Dimensions.get('window');

const PlayerScreen = ({ route, navigation }) => {
  const { item, startTime = 0 } = route.params;

  const [loading, setLoading] = useState(true);
  const [streamUrl, setStreamUrl] = useState(null);
  const [availableStreams, setAvailableStreams] = useState([]);
  const [availableSubtitles, setAvailableSubtitles] = useState([]);
  const [selectedQuality, setSelectedQuality] = useState(null);
  const [selectedSubtitle, setSelectedSubtitle] = useState(null);
  const [showControls, setShowControls] = useState(true);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [showSubtitleSelector, setShowSubtitleSelector] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(startTime);

  const controlsTimeoutRef = useRef(null);

  // Create video player
  const player = useVideoPlayer(streamUrl, (player) => {
    if (startTime > 0) {
      player.currentTime = startTime;
    }
    player.play();
  });

  useEffect(() => {
    loadStreamData();

    // Handle back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    // Set up orientation change listener
    const subscription = ScreenOrientation.addOrientationChangeListener(handleOrientationChange);

    return () => {
      backHandler.remove();
      subscription?.remove();
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      // Reset orientation when leaving
      ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
    };
  }, []);

  useEffect(() => {
    // Auto-hide controls after 3 seconds
    if (showControls && player?.playing) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, player?.playing]);

  useEffect(() => {
    if (player) {
      const timeUpdateSubscription = player.addListener('timeUpdate', (time) => {
        setPosition(time.currentTime);
        setDuration(time.duration || 0);
      });

      return () => {
        timeUpdateSubscription?.remove();
      };
    }
  }, [player]);

  const handleOrientationChange = (orientationInfo) => {
    const isLandscapeMode = orientationInfo.orientationInfo.orientation === ScreenOrientation.Orientation.LANDSCAPE_LEFT ||
                           orientationInfo.orientationInfo.orientation === ScreenOrientation.Orientation.LANDSCAPE_RIGHT;
    setIsLandscape(isLandscapeMode);
  };

  const loadStreamData = async () => {
    try {
      setLoading(true);

      let streamData;
      if (item.media_type === 'tv') {
        // For TV shows, use the season and episode from the item
        const season = item.season || 1;
        const episode = item.episode || 1;
        streamData = await soraApi.getTVStreams(item.id, season, episode);
      } else {
        streamData = await soraApi.getMovieStreams(item.id);
      }

      console.log('Raw stream data:', streamData);

      const processedStreams = soraApi.processStreamResponse(streamData);
      console.log('Processed streams:', processedStreams);

      // Store all available streams for quality selection
      setAvailableStreams(processedStreams.streams || []);
      setAvailableSubtitles(processedStreams.subtitles || []);

      const bestStream = soraApi.getBestQualityStream(processedStreams);
      console.log('Best stream:', bestStream);

      if (bestStream && bestStream.url) {
        setStreamUrl(bestStream.url);
        setSelectedQuality(bestStream);
      } else {
        throw new Error('No playable stream found');
      }
    } catch (error) {
      console.error('Error loading stream:', error);
      Alert.alert(
        'Playback Error',
        'Unable to load video stream. Please try again later.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBackPress = () => {
    saveProgress();
    navigation.goBack();
    return true;
  };

  const saveProgress = async () => {
    if (duration > 0 && position > 0) {
      const progress = position / duration;
      try {
        // For TV shows, use a unique ID that includes season and episode
        const watchId = item.media_type === 'tv' && item.season && item.episode
          ? `${item.id}_s${item.season}_e${item.episode}`
          : item.id;

        await storageService.updateWatchProgress(
          watchId,
          item.media_type,
          progress,
          duration
        );
      } catch (error) {
        console.error('Error saving progress:', error);
      }
    }
  };

  const handlePlayPause = () => {
    if (player) {
      if (player.playing) {
        player.pause();
      } else {
        player.play();
      }
    }
  };

  const handleQualityChange = (stream) => {
    if (player && stream.url !== streamUrl) {
      const currentTime = player.currentTime;
      setStreamUrl(stream.url);
      setSelectedQuality(stream);
      setShowQualitySelector(false);

      // Resume from current position after quality change
      setTimeout(() => {
        if (player) {
          player.currentTime = currentTime;
          player.play();
        }
      }, 100);
    }
  };

  const handleSubtitleChange = (subtitle) => {
    setSelectedSubtitle(subtitle);
    setShowSubtitleSelector(false);
    // Note: Subtitle implementation would require additional setup
    // This is a placeholder for subtitle selection
  };

  const toggleFullscreen = async () => {
    if (isLandscape) {
      await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
    } else {
      await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
    }
  };

  const handleSeek = (newPosition) => {
    if (player) {
      player.currentTime = newPosition / 1000; // Convert to seconds
      setPosition(newPosition / 1000);
    }
  };

  const handleMute = () => {
    if (player) {
      player.muted = !player.muted;
    }
  };

  const formatTime = (timeInSeconds) => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  if (loading) {
    return (
      <View style={{ 
        flex: 1, 
        backgroundColor: NETFLIX_COLORS.black,
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
        <Text style={{ color: NETFLIX_COLORS.white, marginTop: 20, fontSize: 16 }}>
          Loading video...
        </Text>
      </View>
    );
  }

  if (!streamUrl) {
    return (
      <View style={{ 
        flex: 1, 
        backgroundColor: NETFLIX_COLORS.black,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20
      }}>
        <Ionicons name="warning" size={64} color={NETFLIX_COLORS.primary} />
        <Text style={{ 
          color: NETFLIX_COLORS.white, 
          marginTop: 20, 
          fontSize: 18,
          textAlign: 'center'
        }}>
          Video Unavailable
        </Text>
        <Text style={{ 
          color: NETFLIX_COLORS.lightGray, 
          marginTop: 10, 
          fontSize: 14,
          textAlign: 'center'
        }}>
          This content is currently not available for streaming.
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: NETFLIX_COLORS.primary,
            paddingVertical: 12,
            paddingHorizontal: 30,
            borderRadius: 6,
            marginTop: 20
          }}
          onPress={() => navigation.goBack()}
        >
          <Text style={{ color: NETFLIX_COLORS.white, fontSize: 16, fontWeight: 'bold' }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={{
      flex: 1,
      backgroundColor: NETFLIX_COLORS.black,
      paddingBottom: isLandscape ? 0 : 34 // Account for navigation bar in portrait
    }}>
      <StatusBar hidden={isLandscape} barStyle="light-content" backgroundColor={NETFLIX_COLORS.black} />

      <TouchableOpacity
        style={{ flex: 1 }}
        activeOpacity={1}
        onPress={toggleControls}
      >
        <VideoView
          player={player}
          style={{
            width: isLandscape ? height : width,
            height: isLandscape ? width : height * 0.6,
            backgroundColor: NETFLIX_COLORS.black
          }}
          allowsFullscreen={true}
          allowsPictureInPicture={true}
          nativeControls={false}
        />

        {/* Loading Overlay */}
        {isBuffering && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.3)'
          }}>
            <ActivityIndicator size="large" color={NETFLIX_COLORS.white} />
          </View>
        )}

        {/* Controls Overlay */}
        {showControls && (
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'transparent', 'rgba(0,0,0,0.7)']}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'space-between',
            }}
          >
            {/* Top Controls */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 20,
              paddingTop: 50,
            }}>
              <TouchableOpacity onPress={handleBackPress}>
                <Ionicons name="arrow-back" size={28} color={NETFLIX_COLORS.white} />
              </TouchableOpacity>
              
              <Text style={{
                color: NETFLIX_COLORS.white,
                fontSize: 18,
                fontWeight: 'bold',
                flex: 1,
                textAlign: 'center',
                marginHorizontal: 20
              }} numberOfLines={1}>
                {item.media_type === 'tv' && item.season && item.episode
                  ? `${item.name || item.title} - S${item.season}E${item.episode}`
                  : item.title || item.name}
              </Text>
              
              <View style={{ flexDirection: 'row', gap: 15 }}>
                <TouchableOpacity onPress={() => setShowSubtitleSelector(true)}>
                  <Ionicons name="text" size={28} color={NETFLIX_COLORS.white} />
                </TouchableOpacity>

                <TouchableOpacity onPress={() => setShowQualitySelector(true)}>
                  <Ionicons name="settings" size={28} color={NETFLIX_COLORS.white} />
                </TouchableOpacity>

                <TouchableOpacity onPress={toggleFullscreen}>
                  <Ionicons
                    name={isLandscape ? "contract" : "expand"}
                    size={28}
                    color={NETFLIX_COLORS.white}
                  />
                </TouchableOpacity>

                <TouchableOpacity onPress={handleMute}>
                  <Ionicons
                    name={player?.muted ? "volume-mute" : "volume-high"}
                    size={28}
                    color={NETFLIX_COLORS.white}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Center Play/Pause */}
            <View style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <TouchableOpacity
                onPress={handlePlayPause}
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.5)',
                  borderRadius: 40,
                  width: 80,
                  height: 80,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Ionicons
                  name={player?.playing ? "pause" : "play"}
                  size={40}
                  color={NETFLIX_COLORS.white}
                />
              </TouchableOpacity>
            </View>

            {/* Bottom Controls */}
            <View style={{
              paddingHorizontal: 20,
              paddingBottom: 30,
            }}>
              {/* Progress Bar */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 10,
              }}>
                <Text style={{ color: NETFLIX_COLORS.white, fontSize: 12, minWidth: 50 }}>
                  {formatTime(position)}
                </Text>
                
                <View style={{
                  flex: 1,
                  height: 4,
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  marginHorizontal: 10,
                  borderRadius: 2,
                }}>
                  <View style={{
                    height: '100%',
                    backgroundColor: NETFLIX_COLORS.primary,
                    width: `${duration > 0 ? (position / duration) * 100 : 0}%`,
                    borderRadius: 2,
                  }} />
                </View>
                
                <Text style={{ color: NETFLIX_COLORS.white, fontSize: 12, minWidth: 50, textAlign: 'right' }}>
                  {formatTime(duration)}
                </Text>
              </View>
            </View>
          </LinearGradient>
        )}
      </TouchableOpacity>

      {/* Quality Selector Modal */}
      <Modal
        visible={showQualitySelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowQualitySelector(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <View style={{
            backgroundColor: NETFLIX_COLORS.darkGray,
            borderRadius: 10,
            padding: 20,
            width: '80%',
            maxHeight: '60%',
          }}>
            <Text style={{
              color: NETFLIX_COLORS.white,
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 15,
              textAlign: 'center',
            }}>
              Select Quality
            </Text>

            <FlatList
              data={availableStreams}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    padding: 15,
                    borderBottomWidth: 1,
                    borderBottomColor: NETFLIX_COLORS.mediumGray,
                    backgroundColor: selectedQuality?.url === item.url ? NETFLIX_COLORS.primary : 'transparent',
                  }}
                  onPress={() => handleQualityChange(item)}
                >
                  <Text style={{
                    color: NETFLIX_COLORS.white,
                    fontSize: 16,
                  }}>
                    {item.name || `${item.quality}p`}
                  </Text>
                </TouchableOpacity>
              )}
            />

            <TouchableOpacity
              style={{
                backgroundColor: NETFLIX_COLORS.primary,
                padding: 15,
                borderRadius: 5,
                marginTop: 15,
              }}
              onPress={() => setShowQualitySelector(false)}
            >
              <Text style={{
                color: NETFLIX_COLORS.white,
                textAlign: 'center',
                fontSize: 16,
                fontWeight: 'bold',
              }}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Subtitle Selector Modal */}
      <Modal
        visible={showSubtitleSelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSubtitleSelector(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <View style={{
            backgroundColor: NETFLIX_COLORS.darkGray,
            borderRadius: 10,
            padding: 20,
            width: '80%',
            maxHeight: '60%',
          }}>
            <Text style={{
              color: NETFLIX_COLORS.white,
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 15,
              textAlign: 'center',
            }}>
              Select Subtitles
            </Text>

            <TouchableOpacity
              style={{
                padding: 15,
                borderBottomWidth: 1,
                borderBottomColor: NETFLIX_COLORS.mediumGray,
                backgroundColor: !selectedSubtitle ? NETFLIX_COLORS.primary : 'transparent',
              }}
              onPress={() => handleSubtitleChange(null)}
            >
              <Text style={{
                color: NETFLIX_COLORS.white,
                fontSize: 16,
              }}>
                Off
              </Text>
            </TouchableOpacity>

            <FlatList
              data={availableSubtitles}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    padding: 15,
                    borderBottomWidth: 1,
                    borderBottomColor: NETFLIX_COLORS.mediumGray,
                    backgroundColor: selectedSubtitle?.url === item.url ? NETFLIX_COLORS.primary : 'transparent',
                  }}
                  onPress={() => handleSubtitleChange(item)}
                >
                  <Text style={{
                    color: NETFLIX_COLORS.white,
                    fontSize: 16,
                  }}>
                    {item.lang || 'Unknown Language'}
                  </Text>
                </TouchableOpacity>
              )}
            />

            <TouchableOpacity
              style={{
                backgroundColor: NETFLIX_COLORS.primary,
                padding: 15,
                borderRadius: 5,
                marginTop: 15,
              }}
              onPress={() => setShowSubtitleSelector(false)}
            >
              <Text style={{
                color: NETFLIX_COLORS.white,
                textAlign: 'center',
                fontSize: 16,
                fontWeight: 'bold',
              }}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default PlayerScreen;
