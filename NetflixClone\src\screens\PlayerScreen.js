import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  ActivityIndicator,
  Alert,
  BackHandler,
} from 'react-native';
import { Video } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { NETFLIX_COLORS } from '../utils/constants';
import soraApi from '../services/soraApi';
import storageService from '../services/storage';

const { width, height } = Dimensions.get('window');

const PlayerScreen = ({ route, navigation }) => {
  const { item, startTime = 0 } = route.params;
  const videoRef = useRef(null);
  
  const [loading, setLoading] = useState(true);
  const [streamUrl, setStreamUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(startTime);
  const [showControls, setShowControls] = useState(true);
  const [volume, setVolume] = useState(1.0);
  const [isMuted, setIsMuted] = useState(false);
  const [isBuffering, setIsBuffering] = useState(false);

  const controlsTimeoutRef = useRef(null);

  useEffect(() => {
    loadStreamData();
    
    // Handle back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    
    return () => {
      backHandler.remove();
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Auto-hide controls after 3 seconds
    if (showControls && isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, isPlaying]);

  const loadStreamData = async () => {
    try {
      setLoading(true);

      let streamData;
      if (item.media_type === 'tv') {
        // For TV shows, use the season and episode from the item
        const season = item.season || 1;
        const episode = item.episode || 1;
        streamData = await soraApi.getTVStreams(item.id, season, episode);
      } else {
        streamData = await soraApi.getMovieStreams(item.id);
      }

      console.log('Raw stream data:', streamData);

      const processedStreams = soraApi.processStreamResponse(streamData);
      console.log('Processed streams:', processedStreams);

      const bestStream = soraApi.getBestQualityStream(processedStreams);
      console.log('Best stream:', bestStream);

      if (bestStream && bestStream.url) {
        setStreamUrl(bestStream.url);
      } else {
        throw new Error('No playable stream found');
      }
    } catch (error) {
      console.error('Error loading stream:', error);
      Alert.alert(
        'Playback Error',
        'Unable to load video stream. Please try again later.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBackPress = () => {
    saveProgress();
    navigation.goBack();
    return true;
  };

  const saveProgress = async () => {
    if (duration > 0 && position > 0) {
      const progress = position / duration;
      try {
        // For TV shows, use a unique ID that includes season and episode
        const watchId = item.media_type === 'tv' && item.season && item.episode
          ? `${item.id}_s${item.season}_e${item.episode}`
          : item.id;

        await storageService.updateWatchProgress(
          watchId,
          item.media_type,
          progress,
          duration
        );
      } catch (error) {
        console.error('Error saving progress:', error);
      }
    }
  };

  const handlePlayPause = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
    }
  };

  const handleSeek = async (newPosition) => {
    if (videoRef.current) {
      await videoRef.current.setPositionAsync(newPosition);
      setPosition(newPosition);
    }
  };

  const handleMute = async () => {
    if (videoRef.current) {
      const newMutedState = !isMuted;
      await videoRef.current.setIsMutedAsync(newMutedState);
      setIsMuted(newMutedState);
    }
  };

  const formatTime = (timeInSeconds) => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  if (loading) {
    return (
      <View style={{ 
        flex: 1, 
        backgroundColor: NETFLIX_COLORS.black,
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
        <Text style={{ color: NETFLIX_COLORS.white, marginTop: 20, fontSize: 16 }}>
          Loading video...
        </Text>
      </View>
    );
  }

  if (!streamUrl) {
    return (
      <View style={{ 
        flex: 1, 
        backgroundColor: NETFLIX_COLORS.black,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20
      }}>
        <Ionicons name="warning" size={64} color={NETFLIX_COLORS.primary} />
        <Text style={{ 
          color: NETFLIX_COLORS.white, 
          marginTop: 20, 
          fontSize: 18,
          textAlign: 'center'
        }}>
          Video Unavailable
        </Text>
        <Text style={{ 
          color: NETFLIX_COLORS.lightGray, 
          marginTop: 10, 
          fontSize: 14,
          textAlign: 'center'
        }}>
          This content is currently not available for streaming.
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: NETFLIX_COLORS.primary,
            paddingVertical: 12,
            paddingHorizontal: 30,
            borderRadius: 6,
            marginTop: 20
          }}
          onPress={() => navigation.goBack()}
        >
          <Text style={{ color: NETFLIX_COLORS.white, fontSize: 16, fontWeight: 'bold' }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: NETFLIX_COLORS.black }}>
      <StatusBar hidden />
      
      <TouchableOpacity 
        style={{ flex: 1 }}
        activeOpacity={1}
        onPress={toggleControls}
      >
        <Video
          ref={videoRef}
          source={{ uri: streamUrl }}
          style={{ width, height }}
          resizeMode="contain"
          shouldPlay={false}
          isLooping={false}
          volume={volume}
          isMuted={isMuted}
          onLoad={(status) => {
            setDuration(status.durationMillis / 1000);
            if (startTime > 0) {
              handleSeek(startTime * 1000);
            }
          }}
          onPlaybackStatusUpdate={(status) => {
            if (status.isLoaded) {
              setIsPlaying(status.isPlaying);
              setPosition(status.positionMillis / 1000);
              setIsBuffering(status.isBuffering);
            }
          }}
          onError={(error) => {
            console.error('Video error:', error);
            Alert.alert('Playback Error', 'An error occurred during playback.');
          }}
        />

        {/* Loading Overlay */}
        {isBuffering && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.3)'
          }}>
            <ActivityIndicator size="large" color={NETFLIX_COLORS.white} />
          </View>
        )}

        {/* Controls Overlay */}
        {showControls && (
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'transparent', 'rgba(0,0,0,0.7)']}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'space-between',
            }}
          >
            {/* Top Controls */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 20,
              paddingTop: 50,
            }}>
              <TouchableOpacity onPress={handleBackPress}>
                <Ionicons name="arrow-back" size={28} color={NETFLIX_COLORS.white} />
              </TouchableOpacity>
              
              <Text style={{
                color: NETFLIX_COLORS.white,
                fontSize: 18,
                fontWeight: 'bold',
                flex: 1,
                textAlign: 'center',
                marginHorizontal: 20
              }} numberOfLines={1}>
                {item.media_type === 'tv' && item.season && item.episode
                  ? `${item.name || item.title} - S${item.season}E${item.episode}`
                  : item.title || item.name}
              </Text>
              
              <TouchableOpacity onPress={handleMute}>
                <Ionicons 
                  name={isMuted ? "volume-mute" : "volume-high"} 
                  size={28} 
                  color={NETFLIX_COLORS.white} 
                />
              </TouchableOpacity>
            </View>

            {/* Center Play/Pause */}
            <View style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <TouchableOpacity
                onPress={handlePlayPause}
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.5)',
                  borderRadius: 40,
                  width: 80,
                  height: 80,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Ionicons 
                  name={isPlaying ? "pause" : "play"} 
                  size={40} 
                  color={NETFLIX_COLORS.white} 
                />
              </TouchableOpacity>
            </View>

            {/* Bottom Controls */}
            <View style={{
              paddingHorizontal: 20,
              paddingBottom: 30,
            }}>
              {/* Progress Bar */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 10,
              }}>
                <Text style={{ color: NETFLIX_COLORS.white, fontSize: 12, minWidth: 50 }}>
                  {formatTime(position)}
                </Text>
                
                <View style={{
                  flex: 1,
                  height: 4,
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  marginHorizontal: 10,
                  borderRadius: 2,
                }}>
                  <View style={{
                    height: '100%',
                    backgroundColor: NETFLIX_COLORS.primary,
                    width: `${duration > 0 ? (position / duration) * 100 : 0}%`,
                    borderRadius: 2,
                  }} />
                </View>
                
                <Text style={{ color: NETFLIX_COLORS.white, fontSize: 12, minWidth: 50, textAlign: 'right' }}>
                  {formatTime(duration)}
                </Text>
              </View>
            </View>
          </LinearGradient>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default PlayerScreen;
