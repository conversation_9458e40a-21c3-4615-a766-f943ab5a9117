{"version": 3, "names": ["FlingGesture", "ForceTouchGesture", "ComposedGesture", "ExclusiveGesture", "SimultaneousGesture", "LongPressGesture", "PanGesture", "PinchGesture", "RotationGesture", "TapGesture", "NativeGesture", "ManualGesture", "HoverGesture", "GestureObjects", "Tap", "Pan", "Pinch", "Rotation", "Fling", "Long<PERSON>ress", "ForceTouch", "Native", "Manual", "Hover", "Race", "gestures", "Simultaneous", "Exclusive"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureObjects.ts"], "mappings": ";;AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AAEvD,SACEC,eAAe,EACfC,gBAAgB,EAChBC,mBAAmB,QACd,sBAAsB;AAC7B,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5B;AACF;AACA;AACA;EACEC,GAAG,EAAEA,CAAA,KAAM;IACT,OAAO,IAAIL,UAAU,CAAC,CAAC;EACzB,CAAC;EAED;AACF;AACA;AACA;EACEM,GAAG,EAAEA,CAAA,KAAM;IACT,OAAO,IAAIT,UAAU,CAAC,CAAC;EACzB,CAAC;EAED;AACF;AACA;AACA;EACEU,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIT,YAAY,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;EACEU,QAAQ,EAAEA,CAAA,KAAM;IACd,OAAO,IAAIT,eAAe,CAAC,CAAC;EAC9B,CAAC;EAED;AACF;AACA;AACA;EACEU,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIlB,YAAY,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;EACEmB,SAAS,EAAEA,CAAA,KAAM;IACf,OAAO,IAAId,gBAAgB,CAAC,CAAC;EAC/B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEe,UAAU,EAAEA,CAAA,KAAM;IAChB,OAAO,IAAInB,iBAAiB,CAAC,CAAC;EAChC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEoB,MAAM,EAAEA,CAAA,KAAM;IACZ,OAAO,IAAIX,aAAa,CAAC,CAAC;EAC5B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEY,MAAM,EAAEA,CAAA,KAAM;IACZ,OAAO,IAAIX,aAAa,CAAC,CAAC;EAC5B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEY,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIX,YAAY,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;AACA;EACEY,IAAI,EAAEA,CAAC,GAAGC,QAAmB,KAAK;IAChC,OAAO,IAAIvB,eAAe,CAAC,GAAGuB,QAAQ,CAAC;EACzC,CAAC;EAED;AACF;AACA;AACA;EACEC,YAAYA,CAAC,GAAGD,QAAmB,EAAE;IACnC,OAAO,IAAIrB,mBAAmB,CAAC,GAAGqB,QAAQ,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,SAASA,CAAC,GAAGF,QAAmB,EAAE;IAChC,OAAO,IAAItB,gBAAgB,CAAC,GAAGsB,QAAQ,CAAC;EAC1C;AACF,CAAC", "ignoreList": []}