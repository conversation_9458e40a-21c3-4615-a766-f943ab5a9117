import React, { memo, useMemo } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { netflixStyles } from '../styles/netflix';
import tmdbApi from '../services/tmdbApi';

const MediaCard = memo(({
  item,
  onPress,
  size = 'normal',
  showProgress = false,
  progress = 0
}) => {
  const scaleValue = useMemo(() => new Animated.Value(1), []);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 1.05,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const imageUrl = useMemo(() => {
    if (item.poster_path) {
      return tmdbApi.getPosterUrl(item.poster_path);
    }
    if (item.backdrop_path) {
      return tmdbApi.getBackdropUrl(item.backdrop_path, 'w500');
    }
    return null;
  }, [item.poster_path, item.backdrop_path]);

  const title = useMemo(() => {
    return item.title || item.name || 'Unknown Title';
  }, [item.title, item.name]);

  const styles = useMemo(() => ({
    card: size === 'large' ? netflixStyles.mediaCardLarge : netflixStyles.mediaCard,
    image: size === 'large' ? netflixStyles.mediaCardImageLarge : netflixStyles.mediaCardImage,
    title: size === 'large' ? netflixStyles.mediaCardTitleLarge : netflixStyles.mediaCardTitle,
  }), [size]);

  return (
    <Animated.View style={[styles.card, { transform: [{ scale: scaleValue }] }]}>
      <TouchableOpacity
        onPress={() => onPress(item)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <View style={{ position: 'relative' }}>
          <Image
            source={{ uri: imageUrl }}
            style={styles.image}
            defaultSource={require('../../assets/icon.png')}
            resizeMode="cover"
          />

          {showProgress && progress > 0 && (
            <View style={netflixStyles.progressContainer}>
              <View
                style={[
                  netflixStyles.progressBar,
                  { width: `${progress * 100}%` }
                ]}
              />
            </View>
          )}
        </View>

        <Text style={styles.title} numberOfLines={2}>
          {title}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
});

export default MediaCard;
