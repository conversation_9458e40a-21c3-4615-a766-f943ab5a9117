{"version": 3, "names": ["createHandler", "baseGestureHandlerProps", "longPressGestureHandlerProps", "longPressHandlerName", "LongPressGestureHandler", "name", "allowedProps", "config", "shouldCancelWhenOutside"], "sourceRoot": "../../../src", "sources": ["handlers/LongPressGestureHandler.ts"], "mappings": ";;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;AAE/B,OAAO,MAAMC,4BAA4B,GAAG,CAC1C,eAAe,EACf,SAAS,EACT,kBAAkB,CACV;;AAuBV;AACA;AACA;;AAKA,OAAO,MAAMC,oBAAoB,GAAG,yBAAyB;;AAE7D;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGJ,aAAa,CAGlD;EACAK,IAAI,EAAEF,oBAAoB;EAC1BG,YAAY,EAAE,CACZ,GAAGL,uBAAuB,EAC1B,GAAGC,4BAA4B,CACvB;EACVK,MAAM,EAAE;IACNC,uBAAuB,EAAE;EAC3B;AACF,CAAC,CAAC", "ignoreList": []}