{"version": 3, "names": ["_State", "require", "_constants", "_interfaces", "_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "e", "__esModule", "default", "DEFAULT_MIN_POINTERS", "DEFAULT_MAX_POINTERS", "DEFAULT_MIN_DIST_SQ", "DEFAULT_TOUCH_SLOP", "PanGestureHandler", "Gesture<PERSON>andler", "customActivationProperties", "velocityX", "velocityY", "minDistSq", "activeOffsetXStart", "Number", "MAX_SAFE_INTEGER", "activeOffsetXEnd", "MIN_SAFE_INTEGER", "failOffsetXStart", "failOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "failOffsetYStart", "failOffsetYEnd", "minVelocityX", "minVelocityY", "minVelocitySq", "minPointers", "maxPointers", "startX", "startY", "offsetX", "offsetY", "lastX", "lastY", "activateAfterLongPress", "activationTimeout", "enableTrackpadTwoFingerGesture", "endWheelTimeout", "wheelDevice", "WheelDevice", "UNDETERMINED", "updateGestureConfig", "enabled", "props", "resetConfig", "checkCustomActivationCriteria", "config", "minDist", "undefined", "hasCustomActivationCriteria", "minVelocity", "transformNativeEvent", "translationX", "getTranslationX", "translationY", "getTranslationY", "isNaN", "stylusData", "clearActivationTimeout", "clearTimeout", "updateLastCoords", "x", "y", "tracker", "getAbsoluteCoordsAverage", "updateVelocity", "pointerId", "velocities", "getVelocity", "onPointerDown", "event", "isButtonInConfig", "button", "addToTracker", "tryBegin", "<PERSON><PERSON><PERSON>", "tryToSendTouchEvent", "onPointerAdd", "trackedPointersCount", "state", "State", "ACTIVE", "cancel", "fail", "onPointerUp", "lastCoords", "removeFromTracker", "end", "resetProgress", "onPointerRemove", "onPointerMove", "track", "onPointerOutOfBounds", "shouldCancelWhenOutside", "scheduleWheelEnd", "setTimeout", "onWheel", "MOUSE", "wheelDeltaY", "TOUCHPAD", "begin", "activate", "tryToSendMoveEvent", "shouldActivate", "dx", "dy", "distanceSq", "vx", "vy", "velocitySq", "shouldFail", "BEGAN", "force", "onCancel", "onReset", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/PanGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAEA,IAAAG,eAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAA8C,SAAAI,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C,MAAMG,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,oBAAoB,GAAG,EAAE;AAC/B,MAAMC,mBAAmB,GAAGC,6BAAkB,GAAGA,6BAAkB;AAEpD,MAAMC,iBAAiB,SAASC,uBAAc,CAAC;EAC3CC,0BAA0B,GAAa,CACtD,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,aAAa,CACd;EAEMC,SAAS,GAAG,CAAC;EACbC,SAAS,GAAG,CAAC;EAEZC,SAAS,GAAGP,mBAAmB;EAE/BQ,kBAAkB,GAAG,CAACC,MAAM,CAACC,gBAAgB;EAC7CC,gBAAgB,GAAGF,MAAM,CAACG,gBAAgB;EAC1CC,gBAAgB,GAAGJ,MAAM,CAACG,gBAAgB;EAC1CE,cAAc,GAAGL,MAAM,CAACC,gBAAgB;EAExCK,kBAAkB,GAAGN,MAAM,CAACC,gBAAgB;EAC5CM,gBAAgB,GAAGP,MAAM,CAACG,gBAAgB;EAC1CK,gBAAgB,GAAGR,MAAM,CAACG,gBAAgB;EAC1CM,cAAc,GAAGT,MAAM,CAACC,gBAAgB;EAExCS,YAAY,GAAGV,MAAM,CAACC,gBAAgB;EACtCU,YAAY,GAAGX,MAAM,CAACC,gBAAgB;EACtCW,aAAa,GAAGZ,MAAM,CAACC,gBAAgB;EAEvCY,WAAW,GAAGxB,oBAAoB;EAClCyB,WAAW,GAAGxB,oBAAoB;EAElCyB,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EACVC,OAAO,GAAG,CAAC;EACXC,OAAO,GAAG,CAAC;EACXC,KAAK,GAAG,CAAC;EACTC,KAAK,GAAG,CAAC;EAITC,sBAAsB,GAAG,CAAC;EAC1BC,iBAAiB,GAAG,CAAC;EAErBC,8BAA8B,GAAG,KAAK;EACtCC,eAAe,GAAG,CAAC;EACnBC,WAAW,GAAGC,uBAAW,CAACC,YAAY;EAEvCC,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,IAAI,CAACC,WAAW,CAAC,CAAC;IAElB,KAAK,CAACH,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IACzD,IAAI,CAACE,6BAA6B,CAAC,IAAI,CAACrC,0BAA0B,CAAC;IAEnE,IAAI,IAAI,CAACsC,MAAM,CAACC,OAAO,KAAKC,SAAS,EAAE;MACrC,IAAI,CAACrC,SAAS,GAAG,IAAI,CAACmC,MAAM,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;IAC5D,CAAC,MAAM,IAAI,IAAI,CAACE,2BAA2B,EAAE;MAC3C,IAAI,CAACtC,SAAS,GAAGE,MAAM,CAACC,gBAAgB;IAC1C;IAEA,IAAI,IAAI,CAACgC,MAAM,CAACpB,WAAW,KAAKsB,SAAS,EAAE;MACzC,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACoB,MAAM,CAACpB,WAAW;IAC5C;IAEA,IAAI,IAAI,CAACoB,MAAM,CAACnB,WAAW,KAAKqB,SAAS,EAAE;MACzC,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACmB,MAAM,CAACnB,WAAW;IAC5C;IAEA,IAAI,IAAI,CAACmB,MAAM,CAACI,WAAW,KAAKF,SAAS,EAAE;MACzC,IAAI,CAACzB,YAAY,GAAG,IAAI,CAACuB,MAAM,CAACI,WAAW;MAC3C,IAAI,CAAC1B,YAAY,GAAG,IAAI,CAACsB,MAAM,CAACI,WAAW;IAC7C;IAEA,IAAI,IAAI,CAACJ,MAAM,CAACvB,YAAY,KAAKyB,SAAS,EAAE;MAC1C,IAAI,CAACzB,YAAY,GAAG,IAAI,CAACuB,MAAM,CAACvB,YAAY;IAC9C;IAEA,IAAI,IAAI,CAACuB,MAAM,CAACtB,YAAY,KAAKwB,SAAS,EAAE;MAC1C,IAAI,CAACxB,YAAY,GAAG,IAAI,CAACsB,MAAM,CAACtB,YAAY;IAC9C;IAEA,IAAI,IAAI,CAACsB,MAAM,CAACZ,sBAAsB,KAAKc,SAAS,EAAE;MACpD,IAAI,CAACd,sBAAsB,GAAG,IAAI,CAACY,MAAM,CAACZ,sBAAsB;IAClE;IAEA,IAAI,IAAI,CAACY,MAAM,CAAClC,kBAAkB,KAAKoC,SAAS,EAAE;MAChD,IAAI,CAACpC,kBAAkB,GAAG,IAAI,CAACkC,MAAM,CAAClC,kBAAkB;MAExD,IAAI,IAAI,CAACkC,MAAM,CAAC/B,gBAAgB,KAAKiC,SAAS,EAAE;QAC9C,IAAI,CAACjC,gBAAgB,GAAGF,MAAM,CAACC,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAACgC,MAAM,CAAC/B,gBAAgB,KAAKiC,SAAS,EAAE;MAC9C,IAAI,CAACjC,gBAAgB,GAAG,IAAI,CAAC+B,MAAM,CAAC/B,gBAAgB;MAEpD,IAAI,IAAI,CAAC+B,MAAM,CAAClC,kBAAkB,KAAKoC,SAAS,EAAE;QAChD,IAAI,CAACpC,kBAAkB,GAAGC,MAAM,CAACG,gBAAgB;MACnD;IACF;IAEA,IAAI,IAAI,CAAC8B,MAAM,CAAC7B,gBAAgB,KAAK+B,SAAS,EAAE;MAC9C,IAAI,CAAC/B,gBAAgB,GAAG,IAAI,CAAC6B,MAAM,CAAC7B,gBAAgB;MAEpD,IAAI,IAAI,CAAC6B,MAAM,CAAC5B,cAAc,KAAK8B,SAAS,EAAE;QAC5C,IAAI,CAAC9B,cAAc,GAAGL,MAAM,CAACC,gBAAgB;MAC/C;IACF;IAEA,IAAI,IAAI,CAACgC,MAAM,CAAC5B,cAAc,KAAK8B,SAAS,EAAE;MAC5C,IAAI,CAAC9B,cAAc,GAAG,IAAI,CAAC4B,MAAM,CAAC5B,cAAc;MAEhD,IAAI,IAAI,CAAC4B,MAAM,CAAC7B,gBAAgB,KAAK+B,SAAS,EAAE;QAC9C,IAAI,CAAC/B,gBAAgB,GAAGJ,MAAM,CAACG,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAAC8B,MAAM,CAAC3B,kBAAkB,KAAK6B,SAAS,EAAE;MAChD,IAAI,CAAC7B,kBAAkB,GAAG,IAAI,CAAC2B,MAAM,CAAC3B,kBAAkB;MAExD,IAAI,IAAI,CAAC2B,MAAM,CAAC1B,gBAAgB,KAAK4B,SAAS,EAAE;QAC9C,IAAI,CAAC5B,gBAAgB,GAAGP,MAAM,CAACC,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAACgC,MAAM,CAAC1B,gBAAgB,KAAK4B,SAAS,EAAE;MAC9C,IAAI,CAAC5B,gBAAgB,GAAG,IAAI,CAAC0B,MAAM,CAAC1B,gBAAgB;MAEpD,IAAI,IAAI,CAAC0B,MAAM,CAAC3B,kBAAkB,KAAK6B,SAAS,EAAE;QAChD,IAAI,CAAC7B,kBAAkB,GAAGN,MAAM,CAACG,gBAAgB;MACnD;IACF;IAEA,IAAI,IAAI,CAAC8B,MAAM,CAACzB,gBAAgB,KAAK2B,SAAS,EAAE;MAC9C,IAAI,CAAC3B,gBAAgB,GAAG,IAAI,CAACyB,MAAM,CAACzB,gBAAgB;MAEpD,IAAI,IAAI,CAACyB,MAAM,CAACxB,cAAc,KAAK0B,SAAS,EAAE;QAC5C,IAAI,CAAC1B,cAAc,GAAGT,MAAM,CAACC,gBAAgB;MAC/C;IACF;IAEA,IAAI,IAAI,CAACgC,MAAM,CAACxB,cAAc,KAAK0B,SAAS,EAAE;MAC5C,IAAI,CAAC1B,cAAc,GAAG,IAAI,CAACwB,MAAM,CAACxB,cAAc;MAEhD,IAAI,IAAI,CAACwB,MAAM,CAACzB,gBAAgB,KAAK2B,SAAS,EAAE;QAC9C,IAAI,CAAC3B,gBAAgB,GAAGR,MAAM,CAACG,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAAC8B,MAAM,CAACV,8BAA8B,KAAKY,SAAS,EAAE;MAC5D,IAAI,CAACZ,8BAA8B,GACjC,IAAI,CAACU,MAAM,CAACV,8BAA8B;IAC9C;EACF;EAEUQ,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IAEnB,IAAI,CAAChC,kBAAkB,GAAG,CAACC,MAAM,CAACC,gBAAgB;IAClD,IAAI,CAACC,gBAAgB,GAAGF,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACC,gBAAgB,GAAGJ,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACE,cAAc,GAAGL,MAAM,CAACC,gBAAgB;IAE7C,IAAI,CAACK,kBAAkB,GAAGN,MAAM,CAACC,gBAAgB;IACjD,IAAI,CAACM,gBAAgB,GAAGP,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACK,gBAAgB,GAAGR,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACM,cAAc,GAAGT,MAAM,CAACC,gBAAgB;IAE7C,IAAI,CAACS,YAAY,GAAGV,MAAM,CAACC,gBAAgB;IAC3C,IAAI,CAACU,YAAY,GAAGX,MAAM,CAACC,gBAAgB;IAC3C,IAAI,CAACW,aAAa,GAAGZ,MAAM,CAACC,gBAAgB;IAE5C,IAAI,CAACH,SAAS,GAAGP,mBAAmB;IAEpC,IAAI,CAACsB,WAAW,GAAGxB,oBAAoB;IACvC,IAAI,CAACyB,WAAW,GAAGxB,oBAAoB;IAEvC,IAAI,CAAC+B,sBAAsB,GAAG,CAAC;EACjC;EAEUiB,oBAAoBA,CAAA,EAAG;IAC/B,MAAMC,YAAoB,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACnD,MAAMC,YAAoB,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAEnD,OAAO;MACL,GAAG,KAAK,CAACJ,oBAAoB,CAAC,CAAC;MAC/BC,YAAY,EAAEI,KAAK,CAACJ,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;MACpDE,YAAY,EAAEE,KAAK,CAACF,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;MACpD7C,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB+C,UAAU,EAAE,IAAI,CAACA;IACnB,CAAC;EACH;EAEQJ,eAAeA,CAAA,EAAW;IAChC,OAAO,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACE,OAAO;EAChD;EACQyB,eAAeA,CAAA,EAAW;IAChC,OAAO,IAAI,CAACtB,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACE,OAAO;EAChD;EAEQ2B,sBAAsBA,CAAA,EAAS;IACrCC,YAAY,CAAC,IAAI,CAACxB,iBAAiB,CAAC;EACtC;EAEQyB,gBAAgBA,CAAA,EAAG;IACzB,MAAM;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,OAAO,CAACC,wBAAwB,CAAC,CAAC;IAExD,IAAI,CAAChC,KAAK,GAAG6B,CAAC;IACd,IAAI,CAAC5B,KAAK,GAAG6B,CAAC;EAChB;EAEQG,cAAcA,CAACC,SAAiB,EAAE;IACxC,MAAMC,UAAU,GAAG,IAAI,CAACJ,OAAO,CAACK,WAAW,CAACF,SAAS,CAAC;IAEtD,IAAI,CAACzD,SAAS,GAAG0D,UAAU,EAAEN,CAAC,IAAI,CAAC;IACnC,IAAI,CAACnD,SAAS,GAAGyD,UAAU,EAAEL,CAAC,IAAI,CAAC;EACrC;;EAEA;EACUO,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACT,OAAO,CAACU,YAAY,CAACH,KAAK,CAAC;IAChC,IAAI,CAACb,UAAU,GAAGa,KAAK,CAACb,UAAU;IAElC,KAAK,CAACY,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAAChC,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IAAI,CAACyC,QAAQ,CAACJ,KAAK,CAAC;IACpB,IAAI,CAACK,UAAU,CAAC,CAAC;IAEjB,IAAI,CAACC,mBAAmB,CAACN,KAAK,CAAC;EACjC;EAEUO,YAAYA,CAACP,KAAmB,EAAQ;IAChD,IAAI,CAACP,OAAO,CAACU,YAAY,CAACH,KAAK,CAAC;IAChC,KAAK,CAACO,YAAY,CAACP,KAAK,CAAC;IACzB,IAAI,CAACI,QAAQ,CAACJ,KAAK,CAAC;IAEpB,IAAI,CAACxC,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IACxC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IAExC,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAAChC,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IAAI,IAAI,CAAC8B,OAAO,CAACe,oBAAoB,GAAG,IAAI,CAACnD,WAAW,EAAE;MACxD,IAAI,IAAI,CAACoD,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;QAC/B,IAAI,CAACC,MAAM,CAAC,CAAC;MACf,CAAC,MAAM;QACL,IAAI,CAACC,IAAI,CAAC,CAAC;MACb;IACF,CAAC,MAAM;MACL,IAAI,CAACR,UAAU,CAAC,CAAC;IACnB;EACF;EAEUS,WAAWA,CAACd,KAAmB,EAAQ;IAC/C,IAAI,CAACb,UAAU,GAAGa,KAAK,CAACb,UAAU;IAElC,KAAK,CAAC2B,WAAW,CAACd,KAAK,CAAC;IACxB,IAAI,IAAI,CAACS,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B,MAAMI,UAAU,GAAG,IAAI,CAACtB,OAAO,CAACC,wBAAwB,CAAC,CAAC;MAC1D,IAAI,CAAChC,KAAK,GAAGqD,UAAU,CAACxB,CAAC;MACzB,IAAI,CAAC5B,KAAK,GAAGoD,UAAU,CAACvB,CAAC;IAC3B;IAEA,IAAI,CAACC,OAAO,CAACuB,iBAAiB,CAAChB,KAAK,CAACJ,SAAS,CAAC;IAE/C,IAAI,IAAI,CAACH,OAAO,CAACe,oBAAoB,KAAK,CAAC,EAAE;MAC3C,IAAI,CAACpB,sBAAsB,CAAC,CAAC;IAC/B;IAEA,IAAI,IAAI,CAACqB,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACM,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACL,IAAI,CAAC,CAAC;IACb;EACF;EAEUM,eAAeA,CAACnB,KAAmB,EAAQ;IACnD,KAAK,CAACmB,eAAe,CAACnB,KAAK,CAAC;IAC5B,IAAI,CAACP,OAAO,CAACuB,iBAAiB,CAAChB,KAAK,CAACJ,SAAS,CAAC;IAE/C,IAAI,CAACpC,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IACxC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IAExC,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAAChC,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IACE,EACE,IAAI,CAAC8C,KAAK,KAAKC,YAAK,CAACC,MAAM,IAC3B,IAAI,CAAClB,OAAO,CAACe,oBAAoB,GAAG,IAAI,CAACpD,WAAW,CACrD,EACD;MACA,IAAI,CAACiD,UAAU,CAAC,CAAC;IACnB;EACF;EAEUe,aAAaA,CAACpB,KAAmB,EAAQ;IACjD,IAAI,CAACP,OAAO,CAAC4B,KAAK,CAACrB,KAAK,CAAC;IACzB,IAAI,CAACb,UAAU,GAAGa,KAAK,CAACb,UAAU;IAElC,IAAI,CAACG,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACK,cAAc,CAACK,KAAK,CAACJ,SAAS,CAAC;IAEpC,IAAI,CAACS,UAAU,CAAC,CAAC;IAEjB,KAAK,CAACe,aAAa,CAACpB,KAAK,CAAC;EAC5B;EAEUsB,oBAAoBA,CAACtB,KAAmB,EAAQ;IACxD,IAAI,IAAI,CAACuB,uBAAuB,EAAE;MAChC;IACF;IAEA,IAAI,CAAC9B,OAAO,CAAC4B,KAAK,CAACrB,KAAK,CAAC;IACzB,IAAI,CAACb,UAAU,GAAGa,KAAK,CAACb,UAAU;IAElC,IAAI,CAACG,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACK,cAAc,CAACK,KAAK,CAACJ,SAAS,CAAC;IAEpC,IAAI,CAACS,UAAU,CAAC,CAAC;IAEjB,IAAI,IAAI,CAACI,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B,KAAK,CAACW,oBAAoB,CAACtB,KAAK,CAAC;IACnC;EACF;EAEQwB,gBAAgBA,CAACxB,KAAmB,EAAE;IAC5CX,YAAY,CAAC,IAAI,CAACtB,eAAe,CAAC;IAElC,IAAI,CAACA,eAAe,GAAG0D,UAAU,CAAC,MAAM;MACtC,IAAI,IAAI,CAAChB,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;QAC/B,IAAI,CAACM,GAAG,CAAC,CAAC;QACV,IAAI,CAACxB,OAAO,CAACuB,iBAAiB,CAAChB,KAAK,CAACJ,SAAS,CAAC;QAC/C,IAAI,CAACa,KAAK,GAAGC,YAAK,CAACxC,YAAY;MACjC;MAEA,IAAI,CAACF,WAAW,GAAGC,uBAAW,CAACC,YAAY;IAC7C,CAAC,EAAE,EAAE,CAAC;EACR;EAEUwD,OAAOA,CAAC1B,KAAmB,EAAQ;IAC3C,IACE,IAAI,CAAChC,WAAW,KAAKC,uBAAW,CAAC0D,KAAK,IACtC,CAAC,IAAI,CAAC7D,8BAA8B,EACpC;MACA;IACF;IAEA,IAAI,IAAI,CAAC2C,KAAK,KAAKC,YAAK,CAACxC,YAAY,EAAE;MACrC,IAAI,CAACF,WAAW,GACdgC,KAAK,CAAC4B,WAAW,GAAI,GAAG,KAAK,CAAC,GAC1B3D,uBAAW,CAAC4D,QAAQ,GACpB5D,uBAAW,CAAC0D,KAAK;MAEvB,IAAI,IAAI,CAAC3D,WAAW,KAAKC,uBAAW,CAAC0D,KAAK,EAAE;QAC1C,IAAI,CAACH,gBAAgB,CAACxB,KAAK,CAAC;QAC5B;MACF;MAEA,IAAI,CAACP,OAAO,CAACU,YAAY,CAACH,KAAK,CAAC;MAEhC,IAAI,CAACV,gBAAgB,CAAC,CAAC;MAEvB,IAAI,CAAChC,MAAM,GAAG,IAAI,CAACI,KAAK;MACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;MAExB,IAAI,CAACmE,KAAK,CAAC,CAAC;MACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IACA,IAAI,CAACtC,OAAO,CAAC4B,KAAK,CAACrB,KAAK,CAAC;IAEzB,IAAI,CAACV,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACK,cAAc,CAACK,KAAK,CAACJ,SAAS,CAAC;IAEpC,IAAI,CAACoC,kBAAkB,CAAC,KAAK,EAAEhC,KAAK,CAAC;IACrC,IAAI,CAACwB,gBAAgB,CAACxB,KAAK,CAAC;EAC9B;EAEQiC,cAAcA,CAAA,EAAY;IAChC,MAAMC,EAAU,GAAG,IAAI,CAACnD,eAAe,CAAC,CAAC;IAEzC,IACE,IAAI,CAACzC,kBAAkB,KAAKC,MAAM,CAACC,gBAAgB,IACnD0F,EAAE,GAAG,IAAI,CAAC5F,kBAAkB,EAC5B;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACG,gBAAgB,KAAKF,MAAM,CAACG,gBAAgB,IACjDwF,EAAE,GAAG,IAAI,CAACzF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,MAAM0F,EAAU,GAAG,IAAI,CAAClD,eAAe,CAAC,CAAC;IAEzC,IACE,IAAI,CAACpC,kBAAkB,KAAKN,MAAM,CAACC,gBAAgB,IACnD2F,EAAE,GAAG,IAAI,CAACtF,kBAAkB,EAC5B;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACC,gBAAgB,KAAKP,MAAM,CAACG,gBAAgB,IACjDyF,EAAE,GAAG,IAAI,CAACrF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,MAAMsF,UAAkB,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAE5C,IACE,IAAI,CAAC9F,SAAS,KAAKE,MAAM,CAACC,gBAAgB,IAC1C4F,UAAU,IAAI,IAAI,CAAC/F,SAAS,EAC5B;MACA,OAAO,IAAI;IACb;IAEA,MAAMgG,EAAU,GAAG,IAAI,CAAClG,SAAS;IAEjC,IACE,IAAI,CAACc,YAAY,KAAKV,MAAM,CAACC,gBAAgB,KAC3C,IAAI,CAACS,YAAY,GAAG,CAAC,IAAIoF,EAAE,IAAI,IAAI,CAACpF,YAAY,IAC/C,IAAI,CAACA,YAAY,IAAI,CAAC,IAAI,IAAI,CAACA,YAAY,IAAIoF,EAAG,CAAC,EACtD;MACA,OAAO,IAAI;IACb;IAEA,MAAMC,EAAU,GAAG,IAAI,CAAClG,SAAS;IACjC,IACE,IAAI,CAACc,YAAY,KAAKX,MAAM,CAACC,gBAAgB,KAC3C,IAAI,CAACU,YAAY,GAAG,CAAC,IAAIoF,EAAE,IAAI,IAAI,CAACpF,YAAY,IAC/C,IAAI,CAACA,YAAY,IAAI,CAAC,IAAI,IAAI,CAACA,YAAY,IAAIoF,EAAG,CAAC,EACtD;MACA,OAAO,IAAI;IACb;IAEA,MAAMC,UAAkB,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAE5C,OACE,IAAI,CAACnF,aAAa,KAAKZ,MAAM,CAACC,gBAAgB,IAC9C+F,UAAU,IAAI,IAAI,CAACpF,aAAa;EAEpC;EAEQqF,UAAUA,CAAA,EAAY;IAC5B,MAAMN,EAAU,GAAG,IAAI,CAACnD,eAAe,CAAC,CAAC;IACzC,MAAMoD,EAAU,GAAG,IAAI,CAAClD,eAAe,CAAC,CAAC;IACzC,MAAMmD,UAAU,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAEpC,IAAI,IAAI,CAACvE,sBAAsB,GAAG,CAAC,IAAIwE,UAAU,GAAGtG,mBAAmB,EAAE;MACvE,IAAI,CAACsD,sBAAsB,CAAC,CAAC;MAC7B,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACzC,gBAAgB,KAAKJ,MAAM,CAACG,gBAAgB,IACjDwF,EAAE,GAAG,IAAI,CAACvF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACC,cAAc,KAAKL,MAAM,CAACC,gBAAgB,IAC/C0F,EAAE,GAAG,IAAI,CAACtF,cAAc,EACxB;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACG,gBAAgB,KAAKR,MAAM,CAACG,gBAAgB,IACjDyF,EAAE,GAAG,IAAI,CAACpF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,OACE,IAAI,CAACC,cAAc,KAAKT,MAAM,CAACC,gBAAgB,IAC/C2F,EAAE,GAAG,IAAI,CAACnF,cAAc;EAE5B;EAEQoD,QAAQA,CAACJ,KAAmB,EAAQ;IAC1C,IACE,IAAI,CAACS,KAAK,KAAKC,YAAK,CAACxC,YAAY,IACjC,IAAI,CAACuB,OAAO,CAACe,oBAAoB,IAAI,IAAI,CAACpD,WAAW,EACrD;MACA,IAAI,CAAC8D,aAAa,CAAC,CAAC;MACpB,IAAI,CAAC1D,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,OAAO,GAAG,CAAC;MAChB,IAAI,CAACtB,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAG,CAAC;MAElB,IAAI,CAAC0F,KAAK,CAAC,CAAC;MAEZ,IAAI,IAAI,CAAClE,sBAAsB,GAAG,CAAC,EAAE;QACnC,IAAI,CAACC,iBAAiB,GAAG4D,UAAU,CAAC,MAAM;UACxC,IAAI,CAACM,QAAQ,CAAC,CAAC;QACjB,CAAC,EAAE,IAAI,CAACnE,sBAAsB,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAI,CAAC+B,cAAc,CAACK,KAAK,CAACJ,SAAS,CAAC;IACtC;EACF;EAEQS,UAAUA,CAAA,EAAS;IACzB,IAAI,IAAI,CAACI,KAAK,KAAKC,YAAK,CAAC+B,KAAK,EAAE;MAC9B,IAAI,IAAI,CAACD,UAAU,CAAC,CAAC,EAAE;QACrB,IAAI,CAAC3B,IAAI,CAAC,CAAC;MACb,CAAC,MAAM,IAAI,IAAI,CAACoB,cAAc,CAAC,CAAC,EAAE;QAChC,IAAI,CAACF,QAAQ,CAAC,CAAC;MACjB;IACF;EACF;EAEOA,QAAQA,CAACW,KAAK,GAAG,KAAK,EAAQ;IACnC,IAAI,IAAI,CAACjC,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACO,aAAa,CAAC,CAAC;IACtB;IAEA,KAAK,CAACa,QAAQ,CAACW,KAAK,CAAC;EACvB;EAEUC,QAAQA,CAAA,EAAS;IACzB,IAAI,CAACvD,sBAAsB,CAAC,CAAC;EAC/B;EAEUwD,OAAOA,CAAA,EAAS;IACxB,IAAI,CAACxD,sBAAsB,CAAC,CAAC;EAC/B;EAEU8B,aAAaA,CAAA,EAAS;IAC9B,IAAI,IAAI,CAACT,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B;IACF;IAEA,IAAI,CAACrD,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;EAC1B;AACF;AAACkF,OAAA,CAAAlH,OAAA,GAAAK,iBAAA", "ignoreList": []}