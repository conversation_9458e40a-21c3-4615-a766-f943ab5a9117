import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { netflixStyles } from '../styles/netflix';
import { NETFLIX_COLORS } from '../utils/constants';
import tmdbApi from '../services/tmdbApi';
import storageService from '../services/storage';

const TVEpisodesScreen = ({ route, navigation }) => {
  const { item } = route.params;
  const [loading, setLoading] = useState(true);
  const [seasons, setSeasons] = useState([]);
  const [selectedSeason, setSelectedSeason] = useState(1);
  const [episodes, setEpisodes] = useState([]);
  const [loadingEpisodes, setLoadingEpisodes] = useState(false);

  useEffect(() => {
    loadTVDetails();
  }, []);

  useEffect(() => {
    if (selectedSeason) {
      loadSeasonEpisodes(selectedSeason);
    }
  }, [selectedSeason]);

  const loadTVDetails = async () => {
    try {
      setLoading(true);
      const tvDetails = await tmdbApi.getTVDetails(item.id);
      setSeasons(tvDetails.seasons || []);
      
      // Set first season as default (skip season 0 if it exists)
      const firstSeason = tvDetails.seasons?.find(s => s.season_number > 0) || tvDetails.seasons?.[0];
      if (firstSeason) {
        setSelectedSeason(firstSeason.season_number);
      }
    } catch (error) {
      console.error('Error loading TV details:', error);
      Alert.alert('Error', 'Failed to load TV show details.');
    } finally {
      setLoading(false);
    }
  };

  const loadSeasonEpisodes = async (seasonNumber) => {
    try {
      setLoadingEpisodes(true);
      const seasonDetails = await tmdbApi.getTVSeasonDetails(item.id, seasonNumber);
      setEpisodes(seasonDetails.episodes || []);
    } catch (error) {
      console.error('Error loading season episodes:', error);
      setEpisodes([]);
    } finally {
      setLoadingEpisodes(false);
    }
  };

  const handleEpisodePress = async (episode) => {
    try {
      // Add to watch history
      await storageService.addToWatchHistory({
        id: `${item.id}_s${selectedSeason}_e${episode.episode_number}`,
        type: 'tv',
        title: `${item.name} - S${selectedSeason}E${episode.episode_number}`,
        poster: episode.still_path || item.poster_path,
        backdrop: episode.still_path || item.backdrop_path,
        progress: 0,
        season: selectedSeason,
        episode: episode.episode_number,
        showId: item.id,
        showName: item.name,
      });

      navigation.navigate('Player', { 
        item: { 
          ...item, 
          media_type: 'tv',
          season: selectedSeason,
          episode: episode.episode_number,
          episodeTitle: episode.name,
        }
      });
    } catch (error) {
      console.error('Error starting episode playback:', error);
      Alert.alert('Error', 'Failed to start episode playback.');
    }
  };

  const renderSeasonButton = (season) => (
    <TouchableOpacity
      key={season.season_number}
      style={{
        backgroundColor: selectedSeason === season.season_number 
          ? NETFLIX_COLORS.primary 
          : NETFLIX_COLORS.mediumGray,
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 20,
        marginRight: 10,
      }}
      onPress={() => setSelectedSeason(season.season_number)}
    >
      <Text style={{
        color: NETFLIX_COLORS.white,
        fontSize: 14,
        fontWeight: selectedSeason === season.season_number ? 'bold' : 'normal',
      }}>
        {season.name || `Season ${season.season_number}`}
      </Text>
    </TouchableOpacity>
  );

  const renderEpisode = ({ item: episode, index }) => (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: NETFLIX_COLORS.mediumGray,
      }}
      onPress={() => handleEpisodePress(episode)}
    >
      <View style={{ width: 120, height: 68, marginRight: 15 }}>
        <Image
          source={{ 
            uri: episode.still_path 
              ? tmdbApi.getImageUrl(episode.still_path, 'w300')
              : tmdbApi.getPosterUrl(item.poster_path, 'w300')
          }}
          style={{
            width: '100%',
            height: '100%',
            borderRadius: 6,
            backgroundColor: NETFLIX_COLORS.mediumGray,
          }}
          defaultSource={require('../../assets/icon.png')}
        />
        
        {/* Episode Number Overlay */}
        <View style={{
          position: 'absolute',
          bottom: 5,
          left: 5,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: 4,
          paddingHorizontal: 6,
          paddingVertical: 2,
        }}>
          <Text style={{
            color: NETFLIX_COLORS.white,
            fontSize: 10,
            fontWeight: 'bold',
          }}>
            {episode.episode_number}
          </Text>
        </View>
      </View>

      <View style={{ flex: 1 }}>
        <Text style={{
          color: NETFLIX_COLORS.white,
          fontSize: 16,
          fontWeight: 'bold',
          marginBottom: 5,
        }} numberOfLines={2}>
          {episode.name || `Episode ${episode.episode_number}`}
        </Text>
        
        <Text style={{
          color: NETFLIX_COLORS.lightGray,
          fontSize: 12,
          marginBottom: 5,
        }}>
          {episode.runtime ? `${episode.runtime}m` : ''} • {episode.air_date ? new Date(episode.air_date).getFullYear() : ''}
        </Text>
        
        <Text style={{
          color: NETFLIX_COLORS.lightGray,
          fontSize: 14,
          lineHeight: 18,
        }} numberOfLines={3}>
          {episode.overview || 'No description available.'}
        </Text>
      </View>

      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        width: 40,
      }}>
        <Ionicons name="play-circle" size={32} color={NETFLIX_COLORS.white} />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={netflixStyles.loadingContainer}>
        <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
        <Text style={netflixStyles.loadingText}>Loading episodes...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={netflixStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={NETFLIX_COLORS.black} />
      
      {/* Header */}
      <View style={netflixStyles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={NETFLIX_COLORS.white} />
        </TouchableOpacity>
        <Text style={[netflixStyles.headerTitle, { fontSize: 18 }]} numberOfLines={1}>
          {item.name || item.title}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Season Selector */}
      <View style={{ paddingVertical: 15 }}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 20 }}
        >
          {seasons.map(renderSeasonButton)}
        </ScrollView>
      </View>

      {/* Episodes List */}
      <View style={{ flex: 1 }}>
        {loadingEpisodes ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
            <Text style={netflixStyles.loadingText}>Loading episodes...</Text>
          </View>
        ) : (
          <FlatList
            data={episodes}
            renderItem={renderEpisode}
            keyExtractor={(episode) => `episode-${episode.id}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default TVEpisodesScreen;
