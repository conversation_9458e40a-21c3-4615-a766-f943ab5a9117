{"version": 3, "names": ["_flingGesture", "require", "_forceTouchGesture", "_gestureComposition", "_longPressGesture", "_panGesture", "_pinchGesture", "_rotationGesture", "_tapGesture", "_nativeGesture", "_manualGesture", "_hoverGesture", "GestureObjects", "exports", "Tap", "TapGesture", "Pan", "PanGesture", "Pinch", "PinchGesture", "Rotation", "RotationGesture", "Fling", "FlingGesture", "Long<PERSON>ress", "LongPressGesture", "ForceTouch", "ForceTouchGesture", "Native", "NativeGesture", "Manual", "ManualGesture", "Hover", "HoverGesture", "Race", "gestures", "ComposedGesture", "Simultaneous", "SimultaneousGesture", "Exclusive", "ExclusiveGesture"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureObjects.ts"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAF,OAAA;AAKA,IAAAG,iBAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AACA,IAAAM,gBAAA,GAAAN,OAAA;AACA,IAAAO,WAAA,GAAAP,OAAA;AACA,IAAAQ,cAAA,GAAAR,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AACA,IAAAU,aAAA,GAAAV,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMW,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG;EAC5B;AACF;AACA;AACA;EACEE,GAAG,EAAEA,CAAA,KAAM;IACT,OAAO,IAAIC,sBAAU,CAAC,CAAC;EACzB,CAAC;EAED;AACF;AACA;AACA;EACEC,GAAG,EAAEA,CAAA,KAAM;IACT,OAAO,IAAIC,sBAAU,CAAC,CAAC;EACzB,CAAC;EAED;AACF;AACA;AACA;EACEC,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIC,0BAAY,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;EACEC,QAAQ,EAAEA,CAAA,KAAM;IACd,OAAO,IAAIC,gCAAe,CAAC,CAAC;EAC9B,CAAC;EAED;AACF;AACA;AACA;EACEC,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIC,0BAAY,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;EACEC,SAAS,EAAEA,CAAA,KAAM;IACf,OAAO,IAAIC,kCAAgB,CAAC,CAAC;EAC/B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,UAAU,EAAEA,CAAA,KAAM;IAChB,OAAO,IAAIC,oCAAiB,CAAC,CAAC;EAChC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,MAAM,EAAEA,CAAA,KAAM;IACZ,OAAO,IAAIC,4BAAa,CAAC,CAAC;EAC5B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,MAAM,EAAEA,CAAA,KAAM;IACZ,OAAO,IAAIC,4BAAa,CAAC,CAAC;EAC5B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIC,0BAAY,CAAC,CAAC;EAC3B,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,IAAI,EAAEA,CAAC,GAAGC,QAAmB,KAAK;IAChC,OAAO,IAAIC,mCAAe,CAAC,GAAGD,QAAQ,CAAC;EACzC,CAAC;EAED;AACF;AACA;AACA;EACEE,YAAYA,CAAC,GAAGF,QAAmB,EAAE;IACnC,OAAO,IAAIG,uCAAmB,CAAC,GAAGH,QAAQ,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,SAASA,CAAC,GAAGJ,QAAmB,EAAE;IAChC,OAAO,IAAIK,oCAAgB,CAAC,GAAGL,QAAQ,CAAC;EAC1C;AACF,CAAC", "ignoreList": []}