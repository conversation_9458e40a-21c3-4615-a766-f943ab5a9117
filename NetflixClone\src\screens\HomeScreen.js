import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  ImageBackground,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { netflixStyles } from '../styles/netflix';
import { NETFLIX_COLORS } from '../utils/constants';
import tmdbApi from '../services/tmdbApi';
import MediaRow from '../components/MediaRow';
import ContinueWatching from '../components/ContinueWatching';
import storageService from '../services/storage';

const HomeScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [heroItem, setHeroItem] = useState(null);
  const [trendingMovies, setTrendingMovies] = useState([]);
  const [trendingTV, setTrendingTV] = useState([]);
  const [popularMovies, setPopularMovies] = useState([]);
  const [popularTV, setPopularTV] = useState([]);
  const [topRatedMovies, setTopRatedMovies] = useState([]);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setLoading(true);
      
      // Load all data in parallel
      const [
        trendingMoviesData,
        trendingTVData,
        popularMoviesData,
        popularTVData,
        topRatedMoviesData,
      ] = await Promise.all([
        tmdbApi.getTrending('movie'),
        tmdbApi.getTrending('tv'),
        tmdbApi.getPopularMovies(),
        tmdbApi.getPopularTV(),
        tmdbApi.getTopRatedMovies(),
      ]);

      setTrendingMovies(trendingMoviesData.results || []);
      setTrendingTV(trendingTVData.results || []);
      setPopularMovies(popularMoviesData.results || []);
      setPopularTV(popularTVData.results || []);
      setTopRatedMovies(topRatedMoviesData.results || []);

      // Set hero item from trending movies
      if (trendingMoviesData.results && trendingMoviesData.results.length > 0) {
        setHeroItem(trendingMoviesData.results[0]);
      }
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { 
      item: { ...item, media_type: mediaType },
      mediaType 
    });
  };

  const handlePlayPress = async (item) => {
    // Add to watch history
    await storageService.addToWatchHistory({
      id: item.id,
      type: item.media_type || 'movie',
      title: item.title || item.name,
      poster: item.poster_path,
      backdrop: item.backdrop_path,
      progress: 0,
    });

    navigation.navigate('Player', { 
      item: { ...item, media_type: item.media_type || 'movie' }
    });
  };

  const getProgress = (item) => {
    // This would be implemented to get actual progress from storage
    return 0; // Placeholder
  };

  if (loading) {
    return (
      <View style={netflixStyles.loadingContainer}>
        <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
        <Text style={netflixStyles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={netflixStyles.safeArea}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={NETFLIX_COLORS.black}
        translucent={Platform.OS === 'android'}
      />

      {/* Header */}
      <View style={[
        netflixStyles.header,
        {
          paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 10,
          backgroundColor: NETFLIX_COLORS.black,
        }
      ]}>
        <Text style={netflixStyles.headerTitle}>NETFLIX</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Search')}>
          <Ionicons name="search" size={24} color={NETFLIX_COLORS.white} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={netflixStyles.container}
        contentContainerStyle={netflixStyles.scrollContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={NETFLIX_COLORS.primary}
          />
        }
      >
        {/* Hero Banner */}
        {heroItem && (
          <View style={netflixStyles.heroBanner}>
            <ImageBackground
              source={{ uri: tmdbApi.getBackdropUrl(heroItem.backdrop_path, 'w1280') }}
              style={netflixStyles.heroImage}
              imageStyle={{ resizeMode: 'cover' }}
            >
              <LinearGradient
                colors={[
                  'transparent',
                  'transparent',
                  'rgba(0,0,0,0.3)',
                  'rgba(0,0,0,0.7)',
                  NETFLIX_COLORS.black
                ]}
                style={netflixStyles.heroGradient}
              >
                {/* Netflix-style content positioning */}
                <View style={netflixStyles.heroContent}>
                  {/* Show badge */}
                  <View style={netflixStyles.heroBadge}>
                    <Text style={netflixStyles.heroBadgeText}>N</Text>
                    <Text style={netflixStyles.heroOriginalText}>SERIES</Text>
                  </View>

                  {/* Title */}
                  <Text style={netflixStyles.heroTitle}>
                    {heroItem.title || heroItem.name}
                  </Text>

                  {/* Metadata */}
                  <View style={netflixStyles.heroMetadata}>
                    <Text style={netflixStyles.heroYear}>
                      {new Date(heroItem.release_date || heroItem.first_air_date).getFullYear()}
                    </Text>
                    {heroItem.media_type === 'tv' && (
                      <Text style={netflixStyles.heroSeasons}>
                        {heroItem.number_of_seasons || '1'} Season{(heroItem.number_of_seasons || 1) > 1 ? 's' : ''}
                      </Text>
                    )}
                    <View style={netflixStyles.heroGenre}>
                      <Text style={netflixStyles.heroGenreText}>
                        {heroItem.media_type === 'tv' ? 'TV-MA' : 'Movie'}
                      </Text>
                    </View>
                    <View style={netflixStyles.heroRating}>
                      <Ionicons name="star" size={14} color="#FFD700" />
                      <Text style={netflixStyles.heroRatingText}>
                        {heroItem.vote_average?.toFixed(1)}
                      </Text>
                    </View>
                  </View>

                  {/* Ranking badge */}
                  <View style={netflixStyles.heroRanking}>
                    <Ionicons name="trophy" size={16} color={NETFLIX_COLORS.primary} />
                    <Text style={netflixStyles.heroRankingText}>#1 in TV Shows Today</Text>
                  </View>

                  {/* Description */}
                  <Text style={netflixStyles.heroOverview} numberOfLines={3}>
                    {heroItem.overview}
                  </Text>

                  {/* Action Buttons */}
                  <View style={netflixStyles.heroButtons}>
                    <TouchableOpacity
                      style={netflixStyles.playButton}
                      onPress={() => handlePlayPress(heroItem)}
                    >
                      <Ionicons name="play" size={18} color={NETFLIX_COLORS.black} />
                      <Text style={netflixStyles.playButtonText}>Play</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={netflixStyles.infoButton}
                      onPress={() => handleItemPress(heroItem)}
                    >
                      <Ionicons name="information-circle-outline" size={18} color={NETFLIX_COLORS.white} />
                      <Text style={netflixStyles.infoButtonText}>More Info</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </LinearGradient>
            </ImageBackground>
          </View>
        )}

        {/* Continue Watching */}
        <ContinueWatching 
          onItemPress={handleItemPress}
          onRefresh={handleRefresh}
        />

        {/* Media Rows */}
        <MediaRow
          title="Trending Movies"
          data={trendingMovies}
          onItemPress={handleItemPress}
          size="large"
        />

        <MediaRow
          title="Trending TV Shows"
          data={trendingTV}
          onItemPress={handleItemPress}
        />

        <MediaRow
          title="Popular Movies"
          data={popularMovies}
          onItemPress={handleItemPress}
        />

        <MediaRow
          title="Popular TV Shows"
          data={popularTV}
          onItemPress={handleItemPress}
        />

        <MediaRow
          title="Top Rated Movies"
          data={topRatedMovies}
          onItemPress={handleItemPress}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
