{"version": 3, "names": ["Hammer", "Direction", "GesturePropError", "DraggingGestureHandler", "isnan", "FlingGestureHandler", "name", "NativeGestureClass", "Swipe", "onGestureActivated", "event", "sendEvent", "eventType", "INPUT_MOVE", "isFinal", "<PERSON><PERSON><PERSON><PERSON>", "isGestureRunning", "hasGestureFailed", "INPUT_END", "onRawEvent", "ev", "setTimeout", "cancelEvent", "gesture", "hammer", "get", "options", "enable", "onStart", "getHammerConfig", "pointers", "config", "numberOfPointers", "direction", "getDirection", "getTargetDirections", "directions", "RIGHT", "push", "DIRECTION_RIGHT", "LEFT", "DIRECTION_LEFT", "UP", "DIRECTION_UP", "DOWN", "DIRECTION_DOWN", "getConfig", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "Set", "length", "DIRECTION_NONE", "DIRECTION_ALL", "isGestureEnabledForEvent", "_recognizer", "maxPointers", "pointer<PERSON><PERSON><PERSON>", "validPointerCount", "failed", "success", "updateGestureConfig", "props"], "sourceRoot": "../../../src", "sources": ["web_hammer/FlingGestureHandler.ts"], "mappings": ";;AAAA;AACA;AACA,OAAOA,MAAM,MAAM,gBAAgB;AAEnC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,KAAK,QAAQ,SAAS;AAG/B,MAAMC,mBAAmB,SAASF,sBAAsB,CAAC;EACvD,IAAIG,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOP,MAAM,CAACQ,KAAK;EACrB;EAEAC,kBAAkBA,CAACC,KAAqB,EAAE;IACxC,IAAI,CAACC,SAAS,CAAC;MACb,GAAGD,KAAK;MACRE,SAAS,EAAEZ,MAAM,CAACa,UAAU;MAC5BC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACN,SAAS,CAAC;MACb,GAAGD,KAAK;MACRE,SAAS,EAAEZ,MAAM,CAACkB,SAAS;MAC3BJ,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAEAK,UAAUA,CAACC,EAAkB,EAAE;IAC7B,KAAK,CAACD,UAAU,CAACC,EAAE,CAAC;IACpB,IAAI,IAAI,CAACH,gBAAgB,EAAE;MACzB;IACF;IACA;IACA;IACA,IAAIG,EAAE,CAACN,OAAO,EAAE;MACdO,UAAU,CAAC,MAAM;QACf,IAAI,IAAI,CAACL,gBAAgB,EAAE;UACzB,IAAI,CAACM,WAAW,CAACF,EAAE,CAAC;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC,IAAI,CAACH,gBAAgB,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC3D;MACA,MAAMO,OAAO,GAAG,IAAI,CAACC,MAAM,CAAEC,GAAG,CAAC,IAAI,CAACnB,IAAI,CAAC;MAC3C;MACA,IAAIiB,OAAO,CAACG,OAAO,CAACC,MAAM,CAACJ,OAAO,EAAEH,EAAE,CAAC,EAAE;QACvC,IAAI,CAACQ,OAAO,CAACR,EAAE,CAAC;QAChB,IAAI,CAACT,SAAS,CAACS,EAAE,CAAC;MACpB;IACF;EACF;EAEAS,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL;MACAC,QAAQ,EAAE,IAAI,CAACC,MAAM,CAACC,gBAAgB;MACtCC,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC;IAC/B,CAAC;EACH;EAEAC,mBAAmBA,CAACF,SAAiB,EAAE;IACrC,MAAMG,UAAU,GAAG,EAAE;IACrB,IAAIH,SAAS,GAAGhC,SAAS,CAACoC,KAAK,EAAE;MAC/BD,UAAU,CAACE,IAAI,CAACtC,MAAM,CAACuC,eAAe,CAAC;IACzC;IACA,IAAIN,SAAS,GAAGhC,SAAS,CAACuC,IAAI,EAAE;MAC9BJ,UAAU,CAACE,IAAI,CAACtC,MAAM,CAACyC,cAAc,CAAC;IACxC;IACA,IAAIR,SAAS,GAAGhC,SAAS,CAACyC,EAAE,EAAE;MAC5BN,UAAU,CAACE,IAAI,CAACtC,MAAM,CAAC2C,YAAY,CAAC;IACtC;IACA,IAAIV,SAAS,GAAGhC,SAAS,CAAC2C,IAAI,EAAE;MAC9BR,UAAU,CAACE,IAAI,CAACtC,MAAM,CAAC6C,cAAc,CAAC;IACxC;IACA;IACA,OAAOT,UAAU;EACnB;EAEAF,YAAYA,CAAA,EAAG;IACb;IACA,MAAM;MAAED;IAAU,CAAC,GAAG,IAAI,CAACa,SAAS,CAAC,CAAC;IAEtC,IAAIV,UAAU,GAAG,EAAE;IACnB,IAAIH,SAAS,GAAGhC,SAAS,CAACoC,KAAK,EAAE;MAC/BD,UAAU,CAACE,IAAI,CAACtC,MAAM,CAAC+C,oBAAoB,CAAC;IAC9C;IACA,IAAId,SAAS,GAAGhC,SAAS,CAACuC,IAAI,EAAE;MAC9BJ,UAAU,CAACE,IAAI,CAACtC,MAAM,CAAC+C,oBAAoB,CAAC;IAC9C;IACA,IAAId,SAAS,GAAGhC,SAAS,CAACyC,EAAE,EAAE;MAC5BN,UAAU,CAACE,IAAI,CAACtC,MAAM,CAACgD,kBAAkB,CAAC;IAC5C;IACA,IAAIf,SAAS,GAAGhC,SAAS,CAAC2C,IAAI,EAAE;MAC9BR,UAAU,CAACE,IAAI,CAACtC,MAAM,CAACgD,kBAAkB,CAAC;IAC5C;IACAZ,UAAU,GAAG,CAAC,GAAG,IAAIa,GAAG,CAACb,UAAU,CAAC,CAAC;IAErC,IAAIA,UAAU,CAACc,MAAM,KAAK,CAAC,EAAE,OAAOlD,MAAM,CAACmD,cAAc;IACzD,IAAIf,UAAU,CAACc,MAAM,KAAK,CAAC,EAAE,OAAOd,UAAU,CAAC,CAAC,CAAC;IACjD,OAAOpC,MAAM,CAACoD,aAAa;EAC7B;EAEAC,wBAAwBA,CACtB;IAAErB;EAAsB,CAAC,EACzBsB,WAAgB,EAChB;IAAEC,WAAW,EAAEC;EAAmB,CAAC,EACnC;IACA,MAAMC,iBAAiB,GAAGD,aAAa,KAAKxB,gBAAgB;IAC5D,IAAI,CAACyB,iBAAiB,IAAI,IAAI,CAACzC,gBAAgB,EAAE;MAC/C,OAAO;QAAE0C,MAAM,EAAE;MAAK,CAAC;IACzB;IACA,OAAO;MAAEC,OAAO,EAAEF;IAAkB,CAAC;EACvC;EAEAG,mBAAmBA,CAAC;IAAE5B,gBAAgB,GAAG,CAAC;IAAEC,SAAS;IAAE,GAAG4B;EAAW,CAAC,EAAE;IACtE,IAAIzD,KAAK,CAAC6B,SAAS,CAAC,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MACrD,MAAM,IAAI/B,gBAAgB,CAAC,WAAW,EAAE+B,SAAS,EAAE,QAAQ,CAAC;IAC9D;IACA,OAAO,KAAK,CAAC2B,mBAAmB,CAAC;MAC/B5B,gBAAgB;MAChBC,SAAS;MACT,GAAG4B;IACL,CAAC,CAAC;EACJ;AACF;AAEA,eAAexD,mBAAmB", "ignoreList": []}