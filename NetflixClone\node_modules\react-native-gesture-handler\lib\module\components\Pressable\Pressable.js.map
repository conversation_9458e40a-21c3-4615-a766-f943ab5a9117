{"version": 3, "names": ["React", "useCallback", "useLayoutEffect", "useMemo", "useRef", "useState", "GestureObjects", "Gesture", "GestureDetector", "Platform", "processColor", "NativeButton", "gestureToPressableEvent", "addInsets", "numberAsInset", "gestureTouchToPressableEvent", "isTouchWithinInset", "PressabilityDebugView", "INT32_MAX", "isF<PERSON><PERSON>", "isTestEnv", "applyRelationProp", "getConfiguredStateMachine", "StateMachineEvent", "jsx", "_jsx", "jsxs", "_jsxs", "DEFAULT_LONG_PRESS_DURATION", "IS_TEST_ENV", "IS_FABRIC", "Pressable", "props", "ref", "testOnly_pressed", "hitSlop", "pressRetentionOffset", "delayHoverIn", "delayHoverOut", "delayLongPress", "unstable_pressDelay", "onHoverIn", "onHoverOut", "onPress", "onPressIn", "onPressOut", "onLongPress", "style", "children", "android_disableSound", "android_ripple", "disabled", "accessible", "simultaneousWithExternalGesture", "requireExternalGestureToFail", "blocksExternalGesture", "dimensionsAfterResize", "remainingProps", "relationProps", "fallback<PERSON><PERSON>", "pressedState", "setPressedState", "longPressTimeoutRef", "pressDelayTimeoutRef", "isOnPressAllowed", "isCurrentlyPressed", "dimensions", "width", "height", "normalizedHitSlop", "normalizedPressRetentionOffset", "appliedHitSlop", "current", "requestAnimationFrame", "measure", "_x", "_y", "cancelLongPress", "clearTimeout", "cancelDelayedPress", "startLongPress", "event", "setTimeout", "innerHandlePressIn", "handleFinalize", "handlePressIn", "nativeEvent", "changedTouches", "at", "handlePressOut", "success", "stateMachine", "hoverInTimeout", "hoverOutTimeout", "hoverGesture", "Hover", "manualActivation", "cancelsTouchesInView", "onBegin", "onFinalize", "pressAndTouchGesture", "Long<PERSON>ress", "minDuration", "maxDistance", "onTouchesDown", "pressableEvent", "handleEvent", "LONG_PRESS_TOUCHES_DOWN", "onTouchesUp", "OS", "reset", "onTouchesCancelled", "FINALIZE", "buttonGesture", "Native", "NATIVE_BEGIN", "onStart", "NATIVE_START", "isPressableEnabled", "gestures", "gesture", "enabled", "runOnJS", "shouldCancelWhenOutside", "Object", "entries", "for<PERSON>ach", "relationName", "relation", "Simultaneous", "pointerStyle", "cursor", "styleProp", "pressed", "childrenProp", "rippleColor", "defaultRippleColor", "undefined", "unprocessedRippleColor", "color", "touchSoundDisabled", "rippleRadius", "radius", "testOnly_onPress", "testOnly_onPressIn", "testOnly_onPressOut", "testOnly_onLongPress", "__DEV__"], "sourceRoot": "../../../../src", "sources": ["components/Pressable/Pressable.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,WAAW,EACXC,eAAe,EACfC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,OAAO;AACd,SAASC,cAAc,IAAIC,OAAO,QAAQ,wCAAwC;AAClF,SAASC,eAAe,QAAQ,yCAAyC;AAMzE,SAEEC,QAAQ,EAIRC,YAAY,QACP,cAAc;AACrB,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SACEC,uBAAuB,EACvBC,SAAS,EACTC,aAAa,EACbC,4BAA4B,EAC5BC,kBAAkB,QACb,SAAS;AAChB,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,aAAa;AAC5D,SACEC,iBAAiB,QAGZ,UAAU;AACjB,SACEC,yBAAyB,EACzBC,iBAAiB,QACZ,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE5B,MAAMC,2BAA2B,GAAG,GAAG;AACvC,MAAMC,WAAW,GAAGT,SAAS,CAAC,CAAC;AAE/B,IAAIU,SAAyB,GAAG,IAAI;AAEpC,MAAMC,SAAS,GAAIC,KAAqB,IAAK;EAC3C,MAAM;IACJC,GAAG;IACHC,gBAAgB;IAChBC,OAAO;IACPC,oBAAoB;IACpBC,YAAY;IACZC,aAAa;IACbC,cAAc;IACdC,mBAAmB;IACnBC,SAAS;IACTC,UAAU;IACVC,OAAO;IACPC,SAAS;IACTC,UAAU;IACVC,WAAW;IACXC,KAAK;IACLC,QAAQ;IACRC,oBAAoB;IACpBC,cAAc;IACdC,QAAQ;IACRC,UAAU;IACVC,+BAA+B;IAC/BC,4BAA4B;IAC5BC,qBAAqB;IACrBC,qBAAqB;IACrB,GAAGC;EACL,CAAC,GAAGzB,KAAK;EAET,MAAM0B,aAAa,GAAG;IACpBL,+BAA+B;IAC/BC,4BAA4B;IAC5BC;EACF,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGvD,MAAM,CAAO,IAAI,CAAC;EAEtC,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC6B,gBAAgB,IAAI,KAAK,CAAC;EAE3E,MAAM4B,mBAAmB,GAAG1D,MAAM,CAAgB,IAAI,CAAC;EACvD,MAAM2D,oBAAoB,GAAG3D,MAAM,CAAgB,IAAI,CAAC;EACxD,MAAM4D,gBAAgB,GAAG5D,MAAM,CAAU,IAAI,CAAC;EAC9C,MAAM6D,kBAAkB,GAAG7D,MAAM,CAAU,KAAK,CAAC;EACjD,MAAM8D,UAAU,GAAG9D,MAAM,CAAsB;IAAE+D,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAEvE,MAAMC,iBAAyB,GAAGlE,OAAO,CACvC,MACE,OAAOgC,OAAO,KAAK,QAAQ,GAAGrB,aAAa,CAACqB,OAAO,CAAC,GAAIA,OAAO,IAAI,CAAC,CAAE,EACxE,CAACA,OAAO,CACV,CAAC;EAED,MAAMmC,8BAAsC,GAAGnE,OAAO,CACpD,MACE,OAAOiC,oBAAoB,KAAK,QAAQ,GACpCtB,aAAa,CAACsB,oBAAoB,CAAC,GAClCA,oBAAoB,IAAI,CAAC,CAAE,EAClC,CAACA,oBAAoB,CACvB,CAAC;EAED,MAAMmC,cAAc,GAAG1D,SAAS,CAC9BwD,iBAAiB,EACjBC,8BACF,CAAC;EAEDpE,eAAe,CAAC,MAAM;IACpB,IAAIsD,qBAAqB,EAAE;MACzBU,UAAU,CAACM,OAAO,GAAGhB,qBAAqB;IAC5C,CAAC,MAAM;MACLiB,qBAAqB,CAAC,MAAM;QAC1B,CAACxC,GAAG,IAAI0B,WAAW,EAAEa,OAAO,EAAEE,OAAO,CAAC,CAACC,EAAE,EAAEC,EAAE,EAAET,KAAK,EAAEC,MAAM,KAAK;UAC/DF,UAAU,CAACM,OAAO,GAAG;YACnBL,KAAK;YACLC;UACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACZ,qBAAqB,EAAEvB,GAAG,CAAC,CAAC;EAEhC,MAAM4C,eAAe,GAAG5E,WAAW,CAAC,MAAM;IACxC,IAAI6D,mBAAmB,CAACU,OAAO,EAAE;MAC/BM,YAAY,CAAChB,mBAAmB,CAACU,OAAO,CAAC;MACzCV,mBAAmB,CAACU,OAAO,GAAG,IAAI;MAClCR,gBAAgB,CAACQ,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,kBAAkB,GAAG9E,WAAW,CAAC,MAAM;IAC3C,IAAI8D,oBAAoB,CAACS,OAAO,EAAE;MAChCM,YAAY,CAACf,oBAAoB,CAACS,OAAO,CAAC;MAC1CT,oBAAoB,CAACS,OAAO,GAAG,IAAI;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAG/E,WAAW,CAC/BgF,KAAqB,IAAK;IACzB,IAAInC,WAAW,EAAE;MACf+B,eAAe,CAAC,CAAC;MACjBf,mBAAmB,CAACU,OAAO,GAAGU,UAAU,CAAC,MAAM;QAC7ClB,gBAAgB,CAACQ,OAAO,GAAG,KAAK;QAChC1B,WAAW,CAACmC,KAAK,CAAC;MACpB,CAAC,EAAE1C,cAAc,IAAIX,2BAA2B,CAAC;IACnD;EACF,CAAC,EACD,CAACkB,WAAW,EAAE+B,eAAe,EAAEtC,cAAc,CAC/C,CAAC;EAED,MAAM4C,kBAAkB,GAAGlF,WAAW,CACnCgF,KAAqB,IAAK;IACzBrC,SAAS,GAAGqC,KAAK,CAAC;IAClBD,cAAc,CAACC,KAAK,CAAC;IACrBpB,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,oBAAoB,CAACS,OAAO,EAAE;MAChCM,YAAY,CAACf,oBAAoB,CAACS,OAAO,CAAC;MAC1CT,oBAAoB,CAACS,OAAO,GAAG,IAAI;IACrC;EACF,CAAC,EACD,CAAC5B,SAAS,EAAEoC,cAAc,CAC5B,CAAC;EAED,MAAMI,cAAc,GAAGnF,WAAW,CAAC,MAAM;IACvCgE,kBAAkB,CAACO,OAAO,GAAG,KAAK;IAClCK,eAAe,CAAC,CAAC;IACjBE,kBAAkB,CAAC,CAAC;IACpBlB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC,EAAE,CAACkB,kBAAkB,EAAEF,eAAe,CAAC,CAAC;EAEzC,MAAMQ,aAAa,GAAGpF,WAAW,CAC9BgF,KAAqB,IAAK;IACzB,IACE,CAACjE,kBAAkB,CACjBkD,UAAU,CAACM,OAAO,EAClBH,iBAAiB,EACjBY,KAAK,CAACK,WAAW,CAACC,cAAc,CAACC,EAAE,CAAC,CAAC,CAAC,CACxC,CAAC,EACD;MACA;MACA;IACF;IAEAvB,kBAAkB,CAACO,OAAO,GAAG,IAAI;IACjC,IAAIhC,mBAAmB,EAAE;MACvBuB,oBAAoB,CAACS,OAAO,GAAGU,UAAU,CAAC,MAAM;QAC9CC,kBAAkB,CAACF,KAAK,CAAC;MAC3B,CAAC,EAAEzC,mBAAmB,CAAC;IACzB,CAAC,MAAM;MACL2C,kBAAkB,CAACF,KAAK,CAAC;IAC3B;EACF,CAAC,EACD,CAACE,kBAAkB,EAAEd,iBAAiB,EAAE7B,mBAAmB,CAC7D,CAAC;EAED,MAAMiD,cAAc,GAAGxF,WAAW,CAChC,CAACgF,KAAqB,EAAES,OAAgB,GAAG,IAAI,KAAK;IAClD,IAAI,CAACzB,kBAAkB,CAACO,OAAO,EAAE;MAC/B;MACA;IACF;IAEAP,kBAAkB,CAACO,OAAO,GAAG,KAAK;IAElC,IAAIT,oBAAoB,CAACS,OAAO,EAAE;MAChCW,kBAAkB,CAACF,KAAK,CAAC;IAC3B;IAEApC,UAAU,GAAGoC,KAAK,CAAC;IAEnB,IAAIjB,gBAAgB,CAACQ,OAAO,IAAIkB,OAAO,EAAE;MACvC/C,OAAO,GAAGsC,KAAK,CAAC;IAClB;IAEAG,cAAc,CAAC,CAAC;EAClB,CAAC,EACD,CAACA,cAAc,EAAED,kBAAkB,EAAExC,OAAO,EAAEE,UAAU,CAC1D,CAAC;EAED,MAAM8C,YAAY,GAAGxF,OAAO,CAC1B,MAAMmB,yBAAyB,CAAC+D,aAAa,EAAEI,cAAc,CAAC,EAC9D,CAACJ,aAAa,EAAEI,cAAc,CAChC,CAAC;EAED,MAAMG,cAAc,GAAGxF,MAAM,CAAgB,IAAI,CAAC;EAClD,MAAMyF,eAAe,GAAGzF,MAAM,CAAgB,IAAI,CAAC;EAEnD,MAAM0F,YAAY,GAAG3F,OAAO,CAC1B,MACEI,OAAO,CAACwF,KAAK,CAAC,CAAC,CACZC,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAAA,CACvBC,oBAAoB,CAAC,KAAK,CAAC,CAC3BC,OAAO,CAAEjB,KAAK,IAAK;IAClB,IAAIY,eAAe,CAACrB,OAAO,EAAE;MAC3BM,YAAY,CAACe,eAAe,CAACrB,OAAO,CAAC;IACvC;IACA,IAAInC,YAAY,EAAE;MAChBuD,cAAc,CAACpB,OAAO,GAAGU,UAAU,CACjC,MAAMzC,SAAS,GAAG7B,uBAAuB,CAACqE,KAAK,CAAC,CAAC,EACjD5C,YACF,CAAC;MACD;IACF;IACAI,SAAS,GAAG7B,uBAAuB,CAACqE,KAAK,CAAC,CAAC;EAC7C,CAAC,CAAC,CACDkB,UAAU,CAAElB,KAAK,IAAK;IACrB,IAAIW,cAAc,CAACpB,OAAO,EAAE;MAC1BM,YAAY,CAACc,cAAc,CAACpB,OAAO,CAAC;IACtC;IACA,IAAIlC,aAAa,EAAE;MACjBuD,eAAe,CAACrB,OAAO,GAAGU,UAAU,CAClC,MAAMxC,UAAU,GAAG9B,uBAAuB,CAACqE,KAAK,CAAC,CAAC,EAClD3C,aACF,CAAC;MACD;IACF;IACAI,UAAU,GAAG9B,uBAAuB,CAACqE,KAAK,CAAC,CAAC;EAC9C,CAAC,CAAC,EACN,CAAC5C,YAAY,EAAEC,aAAa,EAAEG,SAAS,EAAEC,UAAU,CACrD,CAAC;EAED,MAAM0D,oBAAoB,GAAGjG,OAAO,CAClC,MACEI,OAAO,CAAC8F,SAAS,CAAC,CAAC,CAChBC,WAAW,CAACpF,SAAS,CAAC,CAAC;EAAA,CACvBqF,WAAW,CAACrF,SAAS,CAAC,CAAC;EAAA,CACvB+E,oBAAoB,CAAC,KAAK,CAAC,CAC3BO,aAAa,CAAEvB,KAAK,IAAK;IACxB,MAAMwB,cAAc,GAAG1F,4BAA4B,CAACkE,KAAK,CAAC;IAC1DU,YAAY,CAACe,WAAW,CACtBnF,iBAAiB,CAACoF,uBAAuB,EACzCF,cACF,CAAC;EACH,CAAC,CAAC,CACDG,WAAW,CAAC,MAAM;IACjB,IAAInG,QAAQ,CAACoG,EAAE,KAAK,SAAS,EAAE;MAC7B;MACAlB,YAAY,CAACmB,KAAK,CAAC,CAAC;MACpB1B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,CACD2B,kBAAkB,CAAE9B,KAAK,IAAK;IAC7B,MAAMwB,cAAc,GAAG1F,4BAA4B,CAACkE,KAAK,CAAC;IAC1DU,YAAY,CAACmB,KAAK,CAAC,CAAC;IACpBrB,cAAc,CAACgB,cAAc,EAAE,KAAK,CAAC;EACvC,CAAC,CAAC,CACDN,UAAU,CAAC,MAAM;IAChB,IAAI1F,QAAQ,CAACoG,EAAE,KAAK,KAAK,EAAE;MACzBlB,YAAY,CAACe,WAAW,CAACnF,iBAAiB,CAACyF,QAAQ,CAAC;MACpD5B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,EACN,CAACO,YAAY,EAAEP,cAAc,EAAEK,cAAc,CAC/C,CAAC;;EAED;EACA,MAAMwB,aAAa,GAAG9G,OAAO,CAC3B,MACEI,OAAO,CAAC2G,MAAM,CAAC,CAAC,CACbH,kBAAkB,CAAE9B,KAAK,IAAK;IAC7B,IAAIxE,QAAQ,CAACoG,EAAE,KAAK,OAAO,IAAIpG,QAAQ,CAACoG,EAAE,KAAK,KAAK,EAAE;MACpD;MACA;MACA,MAAMJ,cAAc,GAAG1F,4BAA4B,CAACkE,KAAK,CAAC;MAC1DU,YAAY,CAACmB,KAAK,CAAC,CAAC;MACpBrB,cAAc,CAACgB,cAAc,EAAE,KAAK,CAAC;IACvC;EACF,CAAC,CAAC,CACDP,OAAO,CAAC,MAAM;IACbP,YAAY,CAACe,WAAW,CAACnF,iBAAiB,CAAC4F,YAAY,CAAC;EAC1D,CAAC,CAAC,CACDC,OAAO,CAAC,MAAM;IACb,IAAI3G,QAAQ,CAACoG,EAAE,KAAK,SAAS,EAAE;MAC7B;MACAlB,YAAY,CAACe,WAAW,CAACnF,iBAAiB,CAAC8F,YAAY,CAAC;IAC1D;EACF,CAAC,CAAC,CACDlB,UAAU,CAAC,MAAM;IAChB,IAAI1F,QAAQ,CAACoG,EAAE,KAAK,KAAK,EAAE;MACzB;MACA;MACAlB,YAAY,CAACe,WAAW,CAACnF,iBAAiB,CAACyF,QAAQ,CAAC;MACpD5B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,EACN,CAACO,YAAY,EAAEF,cAAc,EAAEL,cAAc,CAC/C,CAAC;EAED,MAAMkC,kBAAkB,GAAGnE,QAAQ,KAAK,IAAI;EAE5C,MAAMoE,QAAQ,GAAG,CAACN,aAAa,EAAEb,oBAAoB,EAAEN,YAAY,CAAC;EAEpE,KAAK,MAAM0B,OAAO,IAAID,QAAQ,EAAE;IAC9BC,OAAO,CAACC,OAAO,CAACH,kBAAkB,CAAC;IACnCE,OAAO,CAACE,OAAO,CAAC,IAAI,CAAC;IACrBF,OAAO,CAACrF,OAAO,CAACoC,cAAc,CAAC;IAC/BiD,OAAO,CAACG,uBAAuB,CAAClH,QAAQ,CAACoG,EAAE,KAAK,KAAK,CAAC;IAEtDe,MAAM,CAACC,OAAO,CAACnE,aAAa,CAAC,CAACoE,OAAO,CAAC,CAAC,CAACC,YAAY,EAAEC,QAAQ,CAAC,KAAK;MAClE3G,iBAAiB,CACfmG,OAAO,EACPO,YAAY,EACZC,QACF,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,MAAMR,OAAO,GAAGjH,OAAO,CAAC0H,YAAY,CAAC,GAAGV,QAAQ,CAAC;;EAEjD;EACA,MAAMW,YAAkC,GACtCzH,QAAQ,CAACoG,EAAE,KAAK,KAAK,GAAG;IAAEsB,MAAM,EAAE;EAAU,CAAC,GAAG,CAAC,CAAC;EAEpD,MAAMC,SAAS,GACb,OAAOrF,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC;IAAEsF,OAAO,EAAEzE;EAAa,CAAC,CAAC,GAAGb,KAAK;EAExE,MAAMuF,YAAY,GAChB,OAAOtF,QAAQ,KAAK,UAAU,GAC1BA,QAAQ,CAAC;IAAEqF,OAAO,EAAEzE;EAAa,CAAC,CAAC,GACnCZ,QAAQ;EAEd,MAAMuF,WAAW,GAAGpI,OAAO,CAAC,MAAM;IAChC,IAAI2B,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAGX,QAAQ,CAAC,CAAC;IACxB;IAEA,MAAMqH,kBAAkB,GAAGtF,cAAc,GAAGuF,SAAS,GAAG,aAAa;IACrE,MAAMC,sBAAsB,GAAGxF,cAAc,EAAEyF,KAAK,IAAIH,kBAAkB;IAC1E,OAAO1G,SAAS,GACZ4G,sBAAsB,GACtBhI,YAAY,CAACgI,sBAAsB,CAAC;EAC1C,CAAC,EAAE,CAACxF,cAAc,CAAC,CAAC;EAEpB,oBACEzB,IAAA,CAACjB,eAAe;IAACgH,OAAO,EAAEA,OAAQ;IAAAxE,QAAA,eAChCrB,KAAA,CAAChB,YAAY;MAAA,GACP8C,cAAc;MAClBxB,GAAG,EAAEA,GAAG,IAAI0B,WAAY;MACxBP,UAAU,EAAEA,UAAU,KAAK,KAAM;MACjCjB,OAAO,EAAEoC,cAAe;MACxBkD,OAAO,EAAEH,kBAAmB;MAC5BsB,kBAAkB,EAAE3F,oBAAoB,IAAIwF,SAAU;MACtDF,WAAW,EAAEA,WAAY;MACzBM,YAAY,EAAE3F,cAAc,EAAE4F,MAAM,IAAIL,SAAU;MAClD1F,KAAK,EAAE,CAACmF,YAAY,EAAEE,SAAS,CAAE;MACjCW,gBAAgB,EAAElH,WAAW,GAAGc,OAAO,GAAG8F,SAAU;MACpDO,kBAAkB,EAAEnH,WAAW,GAAGe,SAAS,GAAG6F,SAAU;MACxDQ,mBAAmB,EAAEpH,WAAW,GAAGgB,UAAU,GAAG4F,SAAU;MAC1DS,oBAAoB,EAAErH,WAAW,GAAGiB,WAAW,GAAG2F,SAAU;MAAAzF,QAAA,GAC3DsF,YAAY,EACZa,OAAO,gBACN1H,IAAA,CAACR,qBAAqB;QAAC0H,KAAK,EAAC,KAAK;QAACxG,OAAO,EAAEkC;MAAkB,CAAE,CAAC,GAC/D,IAAI;IAAA,CACI;EAAC,CACA,CAAC;AAEtB,CAAC;AAED,eAAetC,SAAS", "ignoreList": []}