{"version": 3, "names": ["createHandler", "baseGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "panHandlerName", "PanGestureHandler", "name", "allowedProps", "config", "transformProps", "managePanProps", "customNativeProps", "validatePanGestureHandlerProps", "props", "Array", "isArray", "activeOffsetX", "Error", "activeOffsetY", "failOffsetX", "failOffsetY", "minDist", "transformPanGestureHandlerProps", "res", "undefined", "activeOffsetXStart", "activeOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "failOffsetXStart", "failOffsetXEnd", "failOffsetYStart", "failOffsetYEnd", "__DEV__"], "sourceRoot": "../../../src", "sources": ["handlers/PanGestureHandler.ts"], "mappings": ";;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;AAE/B,OAAO,MAAMC,sBAAsB,GAAG,CACpC,eAAe,EACf,eAAe,EACf,aAAa,EACb,aAAa,EACb,SAAS,EACT,aAAa,EACb,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,gCAAgC,EAChC,wBAAwB,CAChB;AAEV,OAAO,MAAMC,kCAAkC,GAAG,CAChD,oBAAoB,EACpB,kBAAkB,EAClB,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,kBAAkB,EAClB,gBAAgB,CACR;;AAoDV;AACA;AACA;;AAiDA,OAAO,MAAMC,cAAc,GAAG,mBAAmB;;AAEjD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGL,aAAa,CAG5C;EACAM,IAAI,EAAEF,cAAc;EACpBG,YAAY,EAAE,CACZ,GAAGN,uBAAuB,EAC1B,GAAGC,sBAAsB,CACjB;EACVM,MAAM,EAAE,CAAC,CAAC;EACVC,cAAc,EAAEC,cAAc;EAC9BC,iBAAiB,EAAER;AACrB,CAAC,CAAC;AAEF,SAASS,8BAA8BA,CAACC,KAA6B,EAAE;EACrE,IACEC,KAAK,CAACC,OAAO,CAACF,KAAK,CAACG,aAAa,CAAC,KACjCH,KAAK,CAACG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIH,KAAK,CAACG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAC1D;IACA,MAAM,IAAIC,KAAK,CACb,wFACF,CAAC;EACH;EAEA,IACEH,KAAK,CAACC,OAAO,CAACF,KAAK,CAACK,aAAa,CAAC,KACjCL,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIL,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAC1D;IACA,MAAM,IAAID,KAAK,CACb,wFACF,CAAC;EACH;EAEA,IACEH,KAAK,CAACC,OAAO,CAACF,KAAK,CAACM,WAAW,CAAC,KAC/BN,KAAK,CAACM,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,KAAK,CAACM,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EACtD;IACA,MAAM,IAAIF,KAAK,CACb,sFACF,CAAC;EACH;EAEA,IACEH,KAAK,CAACC,OAAO,CAACF,KAAK,CAACO,WAAW,CAAC,KAC/BP,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIP,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EACtD;IACA,MAAM,IAAIH,KAAK,CACb,sFACF,CAAC;EACH;EAEA,IAAIJ,KAAK,CAACQ,OAAO,KAAKR,KAAK,CAACM,WAAW,IAAIN,KAAK,CAACO,WAAW,CAAC,EAAE;IAC7D,MAAM,IAAIH,KAAK,CACb,iHACF,CAAC;EACH;EAEA,IAAIJ,KAAK,CAACQ,OAAO,KAAKR,KAAK,CAACG,aAAa,IAAIH,KAAK,CAACK,aAAa,CAAC,EAAE;IACjE,MAAM,IAAID,KAAK,CACb,wEACF,CAAC;EACH;AACF;AAEA,SAASK,+BAA+BA,CAACT,KAA6B,EAAE;EAatE,MAAMU,GAAmC,GAAG;IAAE,GAAGV;EAAM,CAAC;EAExD,IAAIA,KAAK,CAACG,aAAa,KAAKQ,SAAS,EAAE;IACrC,OAAOD,GAAG,CAACP,aAAa;IACxB,IAAIF,KAAK,CAACC,OAAO,CAACF,KAAK,CAACG,aAAa,CAAC,EAAE;MACtCO,GAAG,CAACE,kBAAkB,GAAGZ,KAAK,CAACG,aAAa,CAAC,CAAC,CAAC;MAC/CO,GAAG,CAACG,gBAAgB,GAAGb,KAAK,CAACG,aAAa,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIH,KAAK,CAACG,aAAa,GAAG,CAAC,EAAE;MAClCO,GAAG,CAACE,kBAAkB,GAAGZ,KAAK,CAACG,aAAa;IAC9C,CAAC,MAAM;MACLO,GAAG,CAACG,gBAAgB,GAAGb,KAAK,CAACG,aAAa;IAC5C;EACF;EAEA,IAAIH,KAAK,CAACK,aAAa,KAAKM,SAAS,EAAE;IACrC,OAAOD,GAAG,CAACL,aAAa;IACxB,IAAIJ,KAAK,CAACC,OAAO,CAACF,KAAK,CAACK,aAAa,CAAC,EAAE;MACtCK,GAAG,CAACI,kBAAkB,GAAGd,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC;MAC/CK,GAAG,CAACK,gBAAgB,GAAGf,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIL,KAAK,CAACK,aAAa,GAAG,CAAC,EAAE;MAClCK,GAAG,CAACI,kBAAkB,GAAGd,KAAK,CAACK,aAAa;IAC9C,CAAC,MAAM;MACLK,GAAG,CAACK,gBAAgB,GAAGf,KAAK,CAACK,aAAa;IAC5C;EACF;EAEA,IAAIL,KAAK,CAACM,WAAW,KAAKK,SAAS,EAAE;IACnC,OAAOD,GAAG,CAACJ,WAAW;IACtB,IAAIL,KAAK,CAACC,OAAO,CAACF,KAAK,CAACM,WAAW,CAAC,EAAE;MACpCI,GAAG,CAACM,gBAAgB,GAAGhB,KAAK,CAACM,WAAW,CAAC,CAAC,CAAC;MAC3CI,GAAG,CAACO,cAAc,GAAGjB,KAAK,CAACM,WAAW,CAAC,CAAC,CAAC;IAC3C,CAAC,MAAM,IAAIN,KAAK,CAACM,WAAW,GAAG,CAAC,EAAE;MAChCI,GAAG,CAACM,gBAAgB,GAAGhB,KAAK,CAACM,WAAW;IAC1C,CAAC,MAAM;MACLI,GAAG,CAACO,cAAc,GAAGjB,KAAK,CAACM,WAAW;IACxC;EACF;EAEA,IAAIN,KAAK,CAACO,WAAW,KAAKI,SAAS,EAAE;IACnC,OAAOD,GAAG,CAACH,WAAW;IACtB,IAAIN,KAAK,CAACC,OAAO,CAACF,KAAK,CAACO,WAAW,CAAC,EAAE;MACpCG,GAAG,CAACQ,gBAAgB,GAAGlB,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC;MAC3CG,GAAG,CAACS,cAAc,GAAGnB,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC;IAC3C,CAAC,MAAM,IAAIP,KAAK,CAACO,WAAW,GAAG,CAAC,EAAE;MAChCG,GAAG,CAACQ,gBAAgB,GAAGlB,KAAK,CAACO,WAAW;IAC1C,CAAC,MAAM;MACLG,GAAG,CAACS,cAAc,GAAGnB,KAAK,CAACO,WAAW;IACxC;EACF;EAEA,OAAOG,GAAG;AACZ;AAEA,OAAO,SAASb,cAAcA,CAACG,KAA6B,EAAE;EAC5D,IAAIoB,OAAO,EAAE;IACXrB,8BAA8B,CAACC,KAAK,CAAC;EACvC;EACA,OAAOS,+BAA+B,CAACT,KAAK,CAAC;AAC/C", "ignoreList": []}