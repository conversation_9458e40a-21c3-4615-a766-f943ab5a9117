{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_utils", "_PlatformConstants", "_createHandler", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "__esModule", "default", "forceTouchGestureHandlerProps", "exports", "ForceTouchFallback", "React", "Component", "forceTouchAvailable", "componentDidMount", "console", "warn", "tagMessage", "render", "props", "children", "forceTouchHandlerName", "ForceTouchGestureHandler", "PlatformConstants", "createHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/ForceTouchGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,qBAAA,GAAAJ,OAAA;AAGgC,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAGzB,MAAMG,6BAA6B,GAAAC,OAAA,CAAAD,6BAAA,GAAG,CAC3C,UAAU,EACV,UAAU,EACV,sBAAsB,CACd;;AAEV;AACA,MAAME,kBAAkB,SAASC,cAAK,CAACC,SAAS,CAA6B;EAC3E,OAAOC,mBAAmB,GAAG,KAAK;EAClCC,iBAAiBA,CAAA,EAAG;IAClBC,OAAO,CAACC,IAAI,CACV,IAAAC,iBAAU,EACR,8NACF,CACF,CAAC;EACH;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,QAAQ;EAC5B;AACF;;AAuBA;AACA;AACA;;AAKA;AACA;AACA;;AAKO,MAAMC,qBAAqB,GAAAZ,OAAA,CAAAY,qBAAA,GAAG,0BAA0B;;AAE/D;AACA;AACA;AACA;AACO,MAAMC,wBAAwB,GAAAb,OAAA,CAAAa,wBAAA,GAAGC,0BAAiB,EAAEV,mBAAmB,GAC1E,IAAAW,sBAAa,EAGX;EACAC,IAAI,EAAEJ,qBAAqB;EAC3BK,YAAY,EAAE,CACZ,GAAGC,6CAAuB,EAC1B,GAAGnB,6BAA6B,CACxB;EACVoB,MAAM,EAAE,CAAC;AACX,CAAC,CAAC,GACFlB,kBAAkB;AAErBY,wBAAwB,CAA8BT,mBAAmB,GACxEU,0BAAiB,EAAEV,mBAAmB,IAAI,KAAK", "ignoreList": []}