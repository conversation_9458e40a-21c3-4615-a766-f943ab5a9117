{"version": 3, "names": ["State", "TouchEventType", "EventTypes", "GestureHandlerOrchestrator", "InteractionManager", "PointerTracker", "MouseB<PERSON>on", "PointerType", "Gesture<PERSON>andler", "lastSentState", "_state", "UNDETERMINED", "_shouldCancelWhenOutside", "hasCustomActivationCriteria", "_enabled", "_config", "enabled", "_tracker", "_activationIndex", "_awaiting", "_active", "_shouldResetProgress", "_pointerType", "MOUSE", "constructor", "delegate", "_delegate", "init", "viewRef", "propsRef", "state", "attachEventManager", "manager", "setOnPointerDown", "onPointerDown", "bind", "setOnPointerAdd", "onPointerAdd", "setOnPointerUp", "onPointerUp", "setOnPointerRemove", "onPointerRemove", "setOnPointerMove", "onPointerMove", "setOnPointerEnter", "onPointerEnter", "setOnPointerLeave", "onPointerLeave", "setOnPointerCancel", "onPointerCancel", "setOnPointerOutOfBounds", "onPointerOutOfBounds", "setOnPointerMoveOver", "onPointerMoveOver", "setOnPointerMoveOut", "onPointerMoveOut", "setOnWheel", "onWheel", "registerListeners", "onCancel", "onReset", "resetProgress", "reset", "tracker", "resetTracker", "moveToState", "newState", "sendIfDisabled", "oldState", "trackedPointersCount", "config", "needsPointerData", "isFinished", "cancelTouches", "instance", "onHandlerStateChange", "onStateChange", "_newState", "_oldState", "begin", "checkHitSlop", "BEGAN", "fail", "ACTIVE", "onFail", "FAILED", "cancel", "CANCELLED", "activate", "force", "manualActivation", "onActivate", "end", "onEnd", "END", "getShouldResetProgress", "shouldResetProgress", "setShouldResetProgress", "value", "shouldWaitForHandlerFailure", "handler", "shouldRequireToWaitForFailure", "shouldRequireHandlerToWaitForFailure", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "shouldHandlerBeCancelledBy", "event", "recordHandlerIfNotPresent", "pointerType", "TOUCH", "cancelMouseAndPenGestures", "tryToSendTouchEvent", "tryToSendMoveEvent", "shouldCancelWhenOutside", "_event", "out", "active", "sendEvent", "sendTouchEvent", "onGestureHandlerEvent", "current", "touchEvent", "transformTouchEvent", "invokeNullableMethod", "onGestureHandlerStateChange", "resultEvent", "transformEventData", "nativeEvent", "undefined", "numberOfPointers", "pointerInside", "isPointerInBounds", "getAbsoluteCoordsAverage", "transformNativeEvent", "handlerTag", "target", "timeStamp", "Date", "now", "rect", "measure<PERSON>iew", "all", "changed", "trackerData", "trackedPointers", "size", "has", "pointerId", "for<PERSON>ach", "element", "key", "id", "getMappedTouchEventId", "push", "x", "abosoluteCoords", "pageX", "y", "pageY", "absoluteX", "absoluteY", "eventType", "CANCEL", "DOWN", "ADDITIONAL_POINTER_DOWN", "UP", "ADDITIONAL_POINTER_UP", "MOVE", "numberOfTouches", "length", "changedTouches", "allTouches", "cancelEvent", "lastCoords", "lastRelativeCoords", "getRelativeCoordsAverage", "updateGestureConfig", "props", "onEnabledChange", "validateHitSlops", "removeHandlerFromOrchestrator", "checkCustomActivationCriteria", "criterias", "indexOf", "hitSlop", "left", "right", "width", "Error", "height", "top", "bottom", "horizontal", "vertical", "getLastAbsoluteCoords", "offsetX", "offsetY", "isButtonInConfig", "mouseButton", "LEFT", "resetConfig", "onDestroy", "destroy", "_handlerTag", "awaiting", "activationIndex", "getTrackedPointersID", "trackedPointersIDs", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "Array", "isArray", "index", "entries", "nativeValue", "setValue"], "sourceRoot": "../../../../src", "sources": ["web/handlers/GestureHandler.ts"], "mappings": ";;AAAA;AACA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAOEC,cAAc,EACdC,UAAU,QACL,eAAe;AAEtB,OAAOC,0BAA0B,MAAM,qCAAqC;AAC5E,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,OAAOC,cAAc,MAA0B,yBAAyB;AAExE,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,WAAW,QAAQ,mBAAmB;AAG/C,eAAe,MAAeC,cAAc,CAA4B;EAC9DC,aAAa,GAAiB,IAAI;EAElCC,MAAM,GAAUV,KAAK,CAACW,YAAY;EAElCC,wBAAwB,GAAG,KAAK;EAC9BC,2BAA2B,GAAG,KAAK;EACrCC,QAAQ,GAAG,KAAK;EAKhBC,OAAO,GAAW;IAAEC,OAAO,EAAE;EAAM,CAAC;EAEpCC,QAAQ,GAAmB,IAAIZ,cAAc,CAAC,CAAC;;EAEvD;EACQa,gBAAgB,GAAG,CAAC;EAEpBC,SAAS,GAAG,KAAK;EACjBC,OAAO,GAAG,KAAK;EAEfC,oBAAoB,GAAG,KAAK;EAC5BC,YAAY,GAAgBf,WAAW,CAACgB,KAAK;EAI9CC,WAAWA,CAChBC,QAA0D,EAC1D;IACA,IAAI,CAACC,SAAS,GAAGD,QAAQ;EAC3B;;EAEA;EACA;EACA;;EAEUE,IAAIA,CAACC,OAAe,EAAEC,QAAkC,EAAE;IAClE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAACE,KAAK,GAAG9B,KAAK,CAACW,YAAY;IAE/B,IAAI,CAACc,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC;EACnC;EAEOG,kBAAkBA,CAACC,OAA8B,EAAQ;IAC9DA,OAAO,CAACC,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvDH,OAAO,CAACI,eAAe,CAAC,IAAI,CAACC,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IACrDH,OAAO,CAACM,cAAc,CAAC,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;IACnDH,OAAO,CAACQ,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAACN,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3DH,OAAO,CAACU,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IACvDH,OAAO,CAACY,iBAAiB,CAAC,IAAI,CAACC,cAAc,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC;IACzDH,OAAO,CAACc,iBAAiB,CAAC,IAAI,CAACC,cAAc,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;IACzDH,OAAO,CAACgB,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3DH,OAAO,CAACkB,uBAAuB,CAAC,IAAI,CAACC,oBAAoB,CAAChB,IAAI,CAAC,IAAI,CAAC,CAAC;IACrEH,OAAO,CAACoB,oBAAoB,CAAC,IAAI,CAACC,iBAAiB,CAAClB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/DH,OAAO,CAACsB,mBAAmB,CAAC,IAAI,CAACC,gBAAgB,CAACpB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7DH,OAAO,CAACwB,UAAU,CAAC,IAAI,CAACC,OAAO,CAACtB,IAAI,CAAC,IAAI,CAAC,CAAC;IAE3CH,OAAO,CAAC0B,iBAAiB,CAAC,CAAC;EAC7B;;EAEA;EACA;EACA;;EAEUC,QAAQA,CAAA,EAAS,CAAC;EAClBC,OAAOA,CAAA,EAAS,CAAC;EACjBC,aAAaA,CAAA,EAAS,CAAC;EAE1BC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC;IAC3B,IAAI,CAACJ,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACpC,QAAQ,CAACqC,KAAK,CAAC,CAAC;IACrB,IAAI,CAAChC,KAAK,GAAG9B,KAAK,CAACW,YAAY;EACjC;;EAEA;EACA;EACA;;EAEOsD,WAAWA,CAACC,QAAe,EAAEC,cAAwB,EAAE;IAC5D,IAAI,IAAI,CAACrC,KAAK,KAAKoC,QAAQ,EAAE;MAC3B;IACF;IAEA,MAAME,QAAQ,GAAG,IAAI,CAACtC,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGoC,QAAQ;IAErB,IACE,IAAI,CAACH,OAAO,CAACM,oBAAoB,GAAG,CAAC,IACrC,IAAI,CAACC,MAAM,CAACC,gBAAgB,IAC5B,IAAI,CAACC,UAAU,CAAC,CAAC,EACjB;MACA,IAAI,CAACC,aAAa,CAAC,CAAC;IACtB;IAEAtE,0BAA0B,CAACuE,QAAQ,CAACC,oBAAoB,CACtD,IAAI,EACJT,QAAQ,EACRE,QAAQ,EACRD,cACF,CAAC;IAED,IAAI,CAACS,aAAa,CAACV,QAAQ,EAAEE,QAAQ,CAAC;IAEtC,IAAI,CAAC,IAAI,CAACpD,OAAO,IAAI,IAAI,CAACwD,UAAU,CAAC,CAAC,EAAE;MACtC,IAAI,CAAC1C,KAAK,GAAG9B,KAAK,CAACW,YAAY;IACjC;EACF;EAEUiE,aAAaA,CAACC,SAAgB,EAAEC,SAAgB,EAAQ,CAAC;EAE5DC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;MACxB;IACF;IAEA,IAAI,IAAI,CAAClD,KAAK,KAAK9B,KAAK,CAACW,YAAY,EAAE;MACrC,IAAI,CAACsD,WAAW,CAACjE,KAAK,CAACiF,KAAK,CAAC;IAC/B;EACF;;EAEA;AACF;AACA;EACSC,IAAIA,CAACf,cAAwB,EAAQ;IAC1C,IAAI,IAAI,CAACrC,KAAK,KAAK9B,KAAK,CAACmF,MAAM,IAAI,IAAI,CAACrD,KAAK,KAAK9B,KAAK,CAACiF,KAAK,EAAE;MAC7D;MACA;MACA,IAAI,CAACxD,QAAQ,CAAC2D,MAAM,CAAC,CAAC;MAEtB,IAAI,CAACnB,WAAW,CAACjE,KAAK,CAACqF,MAAM,EAAElB,cAAc,CAAC;IAChD;IAEA,IAAI,CAACN,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACSyB,MAAMA,CAACnB,cAAwB,EAAQ;IAC5C,IACE,IAAI,CAACrC,KAAK,KAAK9B,KAAK,CAACmF,MAAM,IAC3B,IAAI,CAACrD,KAAK,KAAK9B,KAAK,CAACW,YAAY,IACjC,IAAI,CAACmB,KAAK,KAAK9B,KAAK,CAACiF,KAAK,EAC1B;MACA,IAAI,CAACtB,QAAQ,CAAC,CAAC;;MAEf;MACA,IAAI,CAAClC,QAAQ,CAACkC,QAAQ,CAAC,CAAC;MAExB,IAAI,CAACM,WAAW,CAACjE,KAAK,CAACuF,SAAS,EAAEpB,cAAc,CAAC;IACnD;EACF;EAEOqB,QAAQA,CAACC,KAAK,GAAG,KAAK,EAAE;IAC7B,IACE,CAAC,IAAI,CAACnB,MAAM,CAACoB,gBAAgB,KAAK,IAAI,IAAID,KAAK,MAC9C,IAAI,CAAC3D,KAAK,KAAK9B,KAAK,CAACW,YAAY,IAAI,IAAI,CAACmB,KAAK,KAAK9B,KAAK,CAACiF,KAAK,CAAC,EACjE;MACA,IAAI,CAACxD,QAAQ,CAACkE,UAAU,CAAC,CAAC;MAC1B,IAAI,CAAC1B,WAAW,CAACjE,KAAK,CAACmF,MAAM,CAAC;IAChC;EACF;EAEOS,GAAGA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC9D,KAAK,KAAK9B,KAAK,CAACiF,KAAK,IAAI,IAAI,CAACnD,KAAK,KAAK9B,KAAK,CAACmF,MAAM,EAAE;MAC7D;MACA,IAAI,CAAC1D,QAAQ,CAACoE,KAAK,CAAC,CAAC;MAErB,IAAI,CAAC5B,WAAW,CAACjE,KAAK,CAAC8F,GAAG,CAAC;IAC7B;IAEA,IAAI,CAACjC,aAAa,CAAC,CAAC;EACtB;;EAEA;EACA;EACA;;EAEOkC,sBAAsBA,CAAA,EAAY;IACvC,OAAO,IAAI,CAACC,mBAAmB;EACjC;EACOC,sBAAsBA,CAACC,KAAc,EAAQ;IAClD,IAAI,CAACF,mBAAmB,GAAGE,KAAK;EAClC;EAEOC,2BAA2BA,CAACC,OAAwB,EAAW;IACpE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,OAAOhG,kBAAkB,CAACsE,QAAQ,CAACyB,2BAA2B,CAC5D,IAAI,EACJC,OACF,CAAC;EACH;EAEOC,6BAA6BA,CAACD,OAAwB,EAAW;IACtE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,OAAOhG,kBAAkB,CAACsE,QAAQ,CAAC4B,oCAAoC,CACrE,IAAI,EACJF,OACF,CAAC;EACH;EAEOG,6BAA6BA,CAACH,OAAwB,EAAW;IACtE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,OAAOhG,kBAAkB,CAACsE,QAAQ,CAAC6B,6BAA6B,CAC9D,IAAI,EACJH,OACF,CAAC;EACH;EAEOI,wBAAwBA,CAACJ,OAAwB,EAAW;IACjE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,OAAOhG,kBAAkB,CAACsE,QAAQ,CAAC+B,0BAA0B,CAC3D,IAAI,EACJL,OACF,CAAC;EACH;;EAEA;EACA;EACA;;EAEUlE,aAAaA,CAACwE,KAAmB,EAAQ;IACjDvG,0BAA0B,CAACuE,QAAQ,CAACiC,yBAAyB,CAAC,IAAI,CAAC;IACnE,IAAI,CAACC,WAAW,GAAGF,KAAK,CAACE,WAAW;IAEpC,IAAI,IAAI,CAACA,WAAW,KAAKrG,WAAW,CAACsG,KAAK,EAAE;MAC1C1G,0BAA0B,CAACuE,QAAQ,CAACoC,yBAAyB,CAAC,IAAI,CAAC;IACrE;;IAEA;EACF;EACA;EACUzE,YAAYA,CAACqE,KAAmB,EAAQ;IAChD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACUnE,WAAWA,CAACmE,KAAmB,EAAQ;IAC/C,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACA;EACUjE,eAAeA,CAACiE,KAAmB,EAAQ;IACnD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACU/D,aAAaA,CAAC+D,KAAmB,EAAQ;IACjD,IAAI,CAACM,kBAAkB,CAAC,KAAK,EAAEN,KAAK,CAAC;EACvC;EACU3D,cAAcA,CAAC2D,KAAmB,EAAQ;IAClD,IAAI,IAAI,CAACO,uBAAuB,EAAE;MAChC,QAAQ,IAAI,CAACnF,KAAK;QAChB,KAAK9B,KAAK,CAACmF,MAAM;UACf,IAAI,CAACG,MAAM,CAAC,CAAC;UACb;QACF,KAAKtF,KAAK,CAACiF,KAAK;UACd,IAAI,CAACC,IAAI,CAAC,CAAC;UACX;MACJ;MACA;IACF;IAEA,IAAI,CAAC6B,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACU7D,cAAcA,CAAC6D,KAAmB,EAAQ;IAClD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACUzD,eAAeA,CAACyD,KAAmB,EAAQ;IACnD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;IAE/B,IAAI,CAACpB,MAAM,CAAC,CAAC;IACb,IAAI,CAACxB,KAAK,CAAC,CAAC;EACd;EACUX,oBAAoBA,CAACuD,KAAmB,EAAQ;IACxD,IAAI,CAACM,kBAAkB,CAAC,IAAI,EAAEN,KAAK,CAAC;EACtC;EACUrD,iBAAiBA,CAAC6D,MAAoB,EAAQ;IACtD;EAAA;EAEQ3D,gBAAgBA,CAAC2D,MAAoB,EAAQ;IACrD;EAAA;EAEQzD,OAAOA,CAACyD,MAAoB,EAAQ;IAC5C;EAAA;EAEQF,kBAAkBA,CAACG,GAAY,EAAET,KAAmB,EAAQ;IACpE,IAAKS,GAAG,IAAI,IAAI,CAACF,uBAAuB,IAAK,CAAC,IAAI,CAACjG,OAAO,EAAE;MAC1D;IACF;IAEA,IAAI,IAAI,CAACoG,MAAM,EAAE;MACf,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC;IACxC;IAEA,IAAI,CAACiF,mBAAmB,CAACL,KAAK,CAAC;EACjC;EAEUK,mBAAmBA,CAACL,KAAmB,EAAQ;IACvD,IAAI,IAAI,CAACpC,MAAM,CAACC,gBAAgB,EAAE;MAChC,IAAI,CAAC+C,cAAc,CAACZ,KAAK,CAAC;IAC5B;EACF;EAEOY,cAAcA,CAACZ,KAAmB,EAAQ;IAC/C,IAAI,CAAC,IAAI,CAAC1F,OAAO,EAAE;MACjB;IACF;IAEA,MAAM;MAAEuG;IAAgC,CAAC,GAAG,IAAI,CAAC1F,QAAQ,CACtD2F,OAAmB;IAEtB,MAAMC,UAAwC,GAC5C,IAAI,CAACC,mBAAmB,CAAChB,KAAK,CAAC;IAEjC,IAAIe,UAAU,EAAE;MACdE,oBAAoB,CAACJ,qBAAqB,EAAEE,UAAU,CAAC;IACzD;EACF;;EAEA;EACA;EACA;;EAEOJ,SAAS,GAAGA,CAACnD,QAAe,EAAEE,QAAe,KAAW;IAC7D,MAAM;MAAEmD,qBAAqB;MAAEK;IAAsC,CAAC,GACpE,IAAI,CAAC/F,QAAQ,CAAC2F,OAAmB;IAEnC,MAAMK,WAAwB,GAAG,IAAI,CAACC,kBAAkB,CACtD5D,QAAQ,EACRE,QACF,CAAC;;IAED;IACA;IACA;IACA;;IAEA,IAAI,IAAI,CAAC3D,aAAa,KAAKyD,QAAQ,EAAE;MACnC,IAAI,CAACzD,aAAa,GAAGyD,QAAQ;MAC7ByD,oBAAoB,CAACC,2BAA2B,EAAEC,WAAW,CAAC;IAChE;IACA,IAAI,IAAI,CAAC/F,KAAK,KAAK9B,KAAK,CAACmF,MAAM,EAAE;MAC/B0C,WAAW,CAACE,WAAW,CAAC3D,QAAQ,GAAG4D,SAAS;MAC5CL,oBAAoB,CAACJ,qBAAqB,EAAEM,WAAW,CAAC;IAC1D;EACF,CAAC;EAEOC,kBAAkBA,CAAC5D,QAAe,EAAEE,QAAe,EAAe;IACxE,OAAO;MACL2D,WAAW,EAAE;QACXE,gBAAgB,EAAE,IAAI,CAAClE,OAAO,CAACM,oBAAoB;QACnDvC,KAAK,EAAEoC,QAAQ;QACfgE,aAAa,EAAE,IAAI,CAACzG,QAAQ,CAAC0G,iBAAiB,CAC5C,IAAI,CAACpE,OAAO,CAACqE,wBAAwB,CAAC,CACxC,CAAC;QACD,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC9BC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,MAAM,EAAE,IAAI,CAAC3G,OAAO;QACpBwC,QAAQ,EAAEF,QAAQ,KAAKE,QAAQ,GAAGA,QAAQ,GAAG4D,SAAS;QACtDpB,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;MACD4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;EAEQhB,mBAAmBA,CACzBhB,KAAmB,EACW;IAC9B,MAAMiC,IAAI,GAAG,IAAI,CAAClH,QAAQ,CAACmH,WAAW,CAAC,CAAC;IAExC,MAAMC,GAAkB,GAAG,EAAE;IAC7B,MAAMC,OAAsB,GAAG,EAAE;IAEjC,MAAMC,WAAW,GAAG,IAAI,CAAChF,OAAO,CAACiF,eAAe;;IAEhD;IACA;IACA;IACA;IACA,IAAID,WAAW,CAACE,IAAI,KAAK,CAAC,IAAI,CAACF,WAAW,CAACG,GAAG,CAACxC,KAAK,CAACyC,SAAS,CAAC,EAAE;MAC/D;IACF;IAEAJ,WAAW,CAACK,OAAO,CAAC,CAACC,OAAuB,EAAEC,GAAW,KAAW;MAClE,MAAMC,EAAU,GAAG,IAAI,CAACxF,OAAO,CAACyF,qBAAqB,CAACF,GAAG,CAAC;MAE1DT,GAAG,CAACY,IAAI,CAAC;QACPF,EAAE,EAAEA,EAAE;QACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;QACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA;IACA,IAAInD,KAAK,CAACuD,SAAS,KAAK/J,UAAU,CAACgK,MAAM,EAAE;MACzCpB,OAAO,CAACW,IAAI,CAAC;QACXF,EAAE,EAAE,IAAI,CAACxF,OAAO,CAACyF,qBAAqB,CAAC9C,KAAK,CAACyC,SAAS,CAAC;QACvDO,CAAC,EAAEhD,KAAK,CAACgD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACvBC,CAAC,EAAEnD,KAAK,CAACmD,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACvBC,SAAS,EAAErD,KAAK,CAACgD,CAAC;QAClBM,SAAS,EAAEtD,KAAK,CAACmD;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLd,WAAW,CAACK,OAAO,CAAC,CAACC,OAAuB,EAAEC,GAAW,KAAW;QAClE,MAAMC,EAAU,GAAG,IAAI,CAACxF,OAAO,CAACyF,qBAAqB,CAACF,GAAG,CAAC;QAE1DR,OAAO,CAACW,IAAI,CAAC;UACXF,EAAE,EAAEA,EAAE;UACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;UACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;UACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;UACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;QACrC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,IAAII,SAAyB,GAAGhK,cAAc,CAACU,YAAY;IAE3D,QAAQ+F,KAAK,CAACuD,SAAS;MACrB,KAAK/J,UAAU,CAACiK,IAAI;MACpB,KAAKjK,UAAU,CAACkK,uBAAuB;QACrCH,SAAS,GAAGhK,cAAc,CAACkK,IAAI;QAC/B;MACF,KAAKjK,UAAU,CAACmK,EAAE;MAClB,KAAKnK,UAAU,CAACoK,qBAAqB;QACnCL,SAAS,GAAGhK,cAAc,CAACoK,EAAE;QAC7B;MACF,KAAKnK,UAAU,CAACqK,IAAI;QAClBN,SAAS,GAAGhK,cAAc,CAACsK,IAAI;QAC/B;MACF,KAAKrK,UAAU,CAACgK,MAAM;QACpBD,SAAS,GAAGhK,cAAc,CAACsF,SAAS;QACpC;IACJ;;IAEA;IACA;IACA;IACA,IAAIiF,eAAuB,GAAG3B,GAAG,CAAC4B,MAAM;IAExC,IACE/D,KAAK,CAACuD,SAAS,KAAK/J,UAAU,CAACmK,EAAE,IACjC3D,KAAK,CAACuD,SAAS,KAAK/J,UAAU,CAACoK,qBAAqB,EACpD;MACA,EAAEE,eAAe;IACnB;IAEA,OAAO;MACLzC,WAAW,EAAE;QACXO,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BxG,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBmI,SAAS,EAAEA,SAAS;QACpBS,cAAc,EAAE5B,OAAO;QACvB6B,UAAU,EAAE9B,GAAG;QACf2B,eAAe,EAAEA,eAAe;QAChC5D,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;MACD4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;EAEQjE,aAAaA,CAAA,EAAS;IAC5B,MAAMkE,IAAI,GAAG,IAAI,CAAClH,QAAQ,CAACmH,WAAW,CAAC,CAAC;IAExC,MAAMC,GAAkB,GAAG,EAAE;IAC7B,MAAMC,OAAsB,GAAG,EAAE;IAEjC,MAAMC,WAAW,GAAG,IAAI,CAAChF,OAAO,CAACiF,eAAe;IAEhD,IAAID,WAAW,CAACE,IAAI,KAAK,CAAC,EAAE;MAC1B;IACF;IAEAF,WAAW,CAACK,OAAO,CAAC,CAACC,OAAuB,EAAEC,GAAW,KAAW;MAClE,MAAMC,EAAU,GAAG,IAAI,CAACxF,OAAO,CAACyF,qBAAqB,CAACF,GAAG,CAAC;MAE1DT,GAAG,CAACY,IAAI,CAAC;QACPF,EAAE,EAAEA,EAAE;QACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;QACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;MACrC,CAAC,CAAC;MAEFf,OAAO,CAACW,IAAI,CAAC;QACXF,EAAE,EAAEA,EAAE;QACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;QACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMe,WAA6B,GAAG;MACpC7C,WAAW,EAAE;QACXO,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BxG,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBmI,SAAS,EAAEhK,cAAc,CAACsF,SAAS;QACnCmF,cAAc,EAAE5B,OAAO;QACvB6B,UAAU,EAAE9B,GAAG;QACf2B,eAAe,EAAE3B,GAAG,CAAC4B,MAAM;QAC3B7D,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;MACD4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,MAAM;MAAEnB;IAAgC,CAAC,GAAG,IAAI,CAAC1F,QAAQ,CACtD2F,OAAmB;IAEtBG,oBAAoB,CAACJ,qBAAqB,EAAEqD,WAAW,CAAC;EAC1D;EAEUvC,oBAAoBA,CAAA,EAA4B;IACxD;IACA,MAAMwC,UAAU,GAAG,IAAI,CAAC9G,OAAO,CAACqE,wBAAwB,CAAC,CAAC;IAC1D,MAAM0C,kBAAkB,GAAG,IAAI,CAAC/G,OAAO,CAACgH,wBAAwB,CAAC,CAAC;IAElE,OAAO;MACLrB,CAAC,EAAEoB,kBAAkB,CAACpB,CAAC;MACvBG,CAAC,EAAEiB,kBAAkB,CAACjB,CAAC;MACvBE,SAAS,EAAEc,UAAU,CAACnB,CAAC;MACvBM,SAAS,EAAEa,UAAU,CAAChB;IACxB,CAAC;EACH;;EAEA;EACA;EACA;;EAEOmB,mBAAmBA,CAAC;IAAEhK,OAAO,GAAG,IAAI;IAAE,GAAGiK;EAAc,CAAC,EAAQ;IACrE,IAAI,CAAClK,OAAO,GAAG;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGiK;IAAM,CAAC;IAC7C,IAAI,CAACjK,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAACS,QAAQ,CAACyJ,eAAe,CAAClK,OAAO,CAAC;IAEtC,IAAI,IAAI,CAACsD,MAAM,CAAC2C,uBAAuB,KAAKe,SAAS,EAAE;MACrD,IAAI,CAACf,uBAAuB,GAAG,IAAI,CAAC3C,MAAM,CAAC2C,uBAAuB;IACpE;IAEA,IAAI,CAACkE,gBAAgB,CAAC,CAAC;IAEvB,IAAI,IAAI,CAACnK,OAAO,EAAE;MAChB;IACF;IAEA,QAAQ,IAAI,CAACc,KAAK;MAChB,KAAK9B,KAAK,CAACmF,MAAM;QACf,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;QACf;MACF,KAAKlF,KAAK,CAACW,YAAY;QACrBR,0BAA0B,CAACuE,QAAQ,CAAC0G,6BAA6B,CAAC,IAAI,CAAC;QACvE;MACF;QACE,IAAI,CAAC9F,MAAM,CAAC,IAAI,CAAC;QACjB;IACJ;EACF;EAEU+F,6BAA6BA,CAACC,SAAmB,EAAQ;IACjE,KAAK,MAAMhC,GAAG,IAAI,IAAI,CAAChF,MAAM,EAAE;MAC7B,IAAIgH,SAAS,CAACC,OAAO,CAACjC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACzI,2BAA2B,GAAG,IAAI;MACzC;IACF;EACF;EAEQsK,gBAAgBA,CAAA,EAAS;IAC/B,IAAI,CAAC,IAAI,CAAC7G,MAAM,CAACkH,OAAO,EAAE;MACxB;IACF;IAEA,IACE,IAAI,CAAClH,MAAM,CAACkH,OAAO,CAACC,IAAI,KAAKzD,SAAS,IACtC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACE,KAAK,KAAK1D,SAAS,IACvC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACG,KAAK,KAAK3D,SAAS,EACvC;MACA,MAAM,IAAI4D,KAAK,CACb,qEACF,CAAC;IACH;IAEA,IACE,IAAI,CAACtH,MAAM,CAACkH,OAAO,CAACG,KAAK,KAAK3D,SAAS,IACvC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACC,IAAI,KAAKzD,SAAS,IACtC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACE,KAAK,KAAK1D,SAAS,EACvC;MACA,MAAM,IAAI4D,KAAK,CACb,8EACF,CAAC;IACH;IAEA,IACE,IAAI,CAACtH,MAAM,CAACkH,OAAO,CAACK,MAAM,KAAK7D,SAAS,IACxC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACM,GAAG,KAAK9D,SAAS,IACrC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACO,MAAM,KAAK/D,SAAS,EACxC;MACA,MAAM,IAAI4D,KAAK,CACb,sEACF,CAAC;IACH;IAEA,IACE,IAAI,CAACtH,MAAM,CAACkH,OAAO,CAACK,MAAM,KAAK7D,SAAS,IACxC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACM,GAAG,KAAK9D,SAAS,IACrC,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACO,MAAM,KAAK/D,SAAS,EACxC;MACA,MAAM,IAAI4D,KAAK,CACb,+EACF,CAAC;IACH;EACF;EAEQ5G,YAAYA,CAAA,EAAY;IAC9B,IAAI,CAAC,IAAI,CAACV,MAAM,CAACkH,OAAO,EAAE;MACxB,OAAO,IAAI;IACb;IAEA,MAAM;MAAEG,KAAK;MAAEE;IAAO,CAAC,GAAG,IAAI,CAACpK,QAAQ,CAACmH,WAAW,CAAC,CAAC;IAErD,IAAI6C,IAAI,GAAG,CAAC;IACZ,IAAIK,GAAG,GAAG,CAAC;IACX,IAAIJ,KAAa,GAAGC,KAAK;IACzB,IAAII,MAAc,GAAGF,MAAM;IAE3B,IAAI,IAAI,CAACvH,MAAM,CAACkH,OAAO,CAACQ,UAAU,KAAKhE,SAAS,EAAE;MAChDyD,IAAI,IAAI,IAAI,CAACnH,MAAM,CAACkH,OAAO,CAACQ,UAAU;MACtCN,KAAK,IAAI,IAAI,CAACpH,MAAM,CAACkH,OAAO,CAACQ,UAAU;IACzC;IAEA,IAAI,IAAI,CAAC1H,MAAM,CAACkH,OAAO,CAACS,QAAQ,KAAKjE,SAAS,EAAE;MAC9C8D,GAAG,IAAI,IAAI,CAACxH,MAAM,CAACkH,OAAO,CAACS,QAAQ;MACnCF,MAAM,IAAI,IAAI,CAACzH,MAAM,CAACkH,OAAO,CAACS,QAAQ;IACxC;IAEA,IAAI,IAAI,CAAC3H,MAAM,CAACkH,OAAO,CAACC,IAAI,KAAKzD,SAAS,EAAE;MAC1CyD,IAAI,GAAG,CAAC,IAAI,CAACnH,MAAM,CAACkH,OAAO,CAACC,IAAI;IAClC;IAEA,IAAI,IAAI,CAACnH,MAAM,CAACkH,OAAO,CAACE,KAAK,KAAK1D,SAAS,EAAE;MAC3C0D,KAAK,GAAGC,KAAK,GAAG,IAAI,CAACrH,MAAM,CAACkH,OAAO,CAACE,KAAK;IAC3C;IAEA,IAAI,IAAI,CAACpH,MAAM,CAACkH,OAAO,CAACM,GAAG,KAAK9D,SAAS,EAAE;MACzC8D,GAAG,GAAG,CAAC,IAAI,CAACxH,MAAM,CAACkH,OAAO,CAACM,GAAG;IAChC;IAEA,IAAI,IAAI,CAACxH,MAAM,CAACkH,OAAO,CAACO,MAAM,KAAK/D,SAAS,EAAE;MAC5C+D,MAAM,GAAGJ,KAAK,GAAG,IAAI,CAACrH,MAAM,CAACkH,OAAO,CAACO,MAAM;IAC7C;IACA,IAAI,IAAI,CAACzH,MAAM,CAACkH,OAAO,CAACG,KAAK,KAAK3D,SAAS,EAAE;MAC3C,IAAI,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACC,IAAI,KAAKzD,SAAS,EAAE;QAC1C0D,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACnH,MAAM,CAACkH,OAAO,CAACG,KAAK;MAC1C,CAAC,MAAM,IAAI,IAAI,CAACrH,MAAM,CAACkH,OAAO,CAACE,KAAK,KAAK1D,SAAS,EAAE;QAClDyD,IAAI,GAAGC,KAAK,GAAG,IAAI,CAACpH,MAAM,CAACkH,OAAO,CAACG,KAAK;MAC1C;IACF;IAEA,IAAI,IAAI,CAACrH,MAAM,CAACkH,OAAO,CAACK,MAAM,KAAK7D,SAAS,EAAE;MAC5C,IAAI,IAAI,CAAC1D,MAAM,CAACkH,OAAO,CAACM,GAAG,KAAK9D,SAAS,EAAE;QACzC+D,MAAM,GAAGD,GAAG,GAAG,IAAI,CAACxH,MAAM,CAACkH,OAAO,CAACK,MAAM;MAC3C,CAAC,MAAM,IAAI,IAAI,CAACvH,MAAM,CAACkH,OAAO,CAACO,MAAM,KAAK/D,SAAS,EAAE;QACnD8D,GAAG,GAAGC,MAAM,GAAG,IAAI,CAACzH,MAAM,CAACkH,OAAO,CAACK,MAAM;MAC3C;IACF;IAEA,MAAMlD,IAAI,GAAG,IAAI,CAAClH,QAAQ,CAACmH,WAAW,CAAC,CAAC;IAExC,MAAMiC,UAAU,GAAG,IAAI,CAAC9G,OAAO,CAACmI,qBAAqB,CAAC,CAAC;IAEvD,IAAI,CAACrB,UAAU,EAAE;MACf,OAAO,KAAK;IACd;IAEA,MAAMsB,OAAe,GAAGtB,UAAU,CAACnB,CAAC,GAAGf,IAAI,CAACiB,KAAK;IACjD,MAAMwC,OAAe,GAAGvB,UAAU,CAAChB,CAAC,GAAGlB,IAAI,CAACmB,KAAK;IAEjD,OACEqC,OAAO,IAAIV,IAAI,IAAIU,OAAO,IAAIT,KAAK,IAAIU,OAAO,IAAIN,GAAG,IAAIM,OAAO,IAAIL,MAAM;EAE9E;EAEOM,gBAAgBA,CAACC,WAAoC,EAAE;IAC5D,OACE,CAACA,WAAW,IACX,CAAC,IAAI,CAAChI,MAAM,CAACgI,WAAW,IAAIA,WAAW,KAAKhM,WAAW,CAACiM,IAAK,IAC7D,IAAI,CAACjI,MAAM,CAACgI,WAAW,IAAIA,WAAW,GAAG,IAAI,CAAChI,MAAM,CAACgI,WAAY;EAEtE;EAEUE,WAAWA,CAAA,EAAS,CAAC;EAExBC,SAASA,CAAA,EAAS;IACvB,IAAI,CAAChL,QAAQ,CAACiL,OAAO,CAAC,IAAI,CAACpI,MAAM,CAAC;EACpC;;EAEA;EACA;EACA;;EAEA,IAAWgE,UAAUA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACqE,WAAW;EACzB;EACA,IAAWrE,UAAUA,CAACpC,KAAa,EAAE;IACnC,IAAI,CAACyG,WAAW,GAAGzG,KAAK;EAC1B;EAEA,IAAW5B,MAAMA,CAAA,EAAW;IAC1B,OAAO,IAAI,CAACvD,OAAO;EACrB;EAEA,IAAWU,QAAQA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,SAAS;EACvB;EAEA,IAAWqC,OAAOA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC9C,QAAQ;EACtB;EAEA,IAAWa,KAAKA,CAAA,EAAU;IACxB,OAAO,IAAI,CAACpB,MAAM;EACpB;EACA,IAAcoB,KAAKA,CAACoE,KAAY,EAAE;IAChC,IAAI,CAACxF,MAAM,GAAGwF,KAAK;EACrB;EAEA,IAAWe,uBAAuBA,CAAA,EAAG;IACnC,OAAO,IAAI,CAACrG,wBAAwB;EACtC;EACA,IAAcqG,uBAAuBA,CAACf,KAAK,EAAE;IAC3C,IAAI,CAACtF,wBAAwB,GAAGsF,KAAK;EACvC;EAEA,IAAWlF,OAAOA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,QAAQ;EACtB;EACA,IAAcE,OAAOA,CAACkF,KAAK,EAAE;IAC3B,IAAI,CAACpF,QAAQ,GAAGoF,KAAK;EACvB;EAEA,IAAWU,WAAWA,CAAA,EAAgB;IACpC,OAAO,IAAI,CAACtF,YAAY;EAC1B;EACA,IAAcsF,WAAWA,CAACV,KAAkB,EAAE;IAC5C,IAAI,CAAC5E,YAAY,GAAG4E,KAAK;EAC3B;EAEA,IAAWkB,MAAMA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAChG,OAAO;EACrB;EACA,IAAcgG,MAAMA,CAAClB,KAAK,EAAE;IAC1B,IAAI,CAAC9E,OAAO,GAAG8E,KAAK;EACtB;EAEA,IAAW0G,QAAQA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACzL,SAAS;EACvB;EACA,IAAcyL,QAAQA,CAAC1G,KAAK,EAAE;IAC5B,IAAI,CAAC/E,SAAS,GAAG+E,KAAK;EACxB;EAEA,IAAW2G,eAAeA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAAC3L,gBAAgB;EAC9B;EACA,IAAc2L,eAAeA,CAAC3G,KAAK,EAAE;IACnC,IAAI,CAAChF,gBAAgB,GAAGgF,KAAK;EAC/B;EAEA,IAAWF,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAAC3E,oBAAoB;EAClC;EACA,IAAc2E,mBAAmBA,CAACE,KAAK,EAAE;IACvC,IAAI,CAAC7E,oBAAoB,GAAG6E,KAAK;EACnC;EAEO4G,oBAAoBA,CAAA,EAAa;IACtC,OAAO,IAAI,CAAC/I,OAAO,CAACgJ,kBAAkB;EACxC;EAEQvI,UAAUA,CAAA,EAAY;IAC5B,OACE,IAAI,CAAC1C,KAAK,KAAK9B,KAAK,CAAC8F,GAAG,IACxB,IAAI,CAAChE,KAAK,KAAK9B,KAAK,CAACqF,MAAM,IAC3B,IAAI,CAACvD,KAAK,KAAK9B,KAAK,CAACuF,SAAS;EAElC;AACF;AAEA,SAASoC,oBAAoBA,CAC3BqF,MAG+C,EAC/CtG,KAAqC,EAC/B;EACN,IAAI,CAACsG,MAAM,EAAE;IACX;EACF;EAEA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChCA,MAAM,CAACtG,KAAK,CAAC;IACb;EACF;EAEA,IAAI,cAAc,IAAIsG,MAAM,IAAI,OAAOA,MAAM,CAACC,YAAY,KAAK,UAAU,EAAE;IACzE,MAAM7G,OAAO,GAAG4G,MAAM,CAACC,YAAY,CAAC,CAAC;IACrCtF,oBAAoB,CAACvB,OAAO,EAAEM,KAAK,CAAC;IACpC;EACF;EAEA,IAAI,EAAE,cAAc,IAAIsG,MAAM,CAAC,EAAE;IAC/B;EACF;EAEA,MAAM;IAAEE;EAAoC,CAAC,GAAGF,MAAM,CAACG,YAAY;EACnE,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;IAC9B;EACF;EAEA,KAAK,MAAM,CAACI,KAAK,EAAE,CAAChE,GAAG,EAAEpD,KAAK,CAAC,CAAC,IAAIgH,UAAU,CAACK,OAAO,CAAC,CAAC,EAAE;IACxD,IAAI,EAAEjE,GAAG,IAAI5C,KAAK,CAACqB,WAAW,CAAC,EAAE;MAC/B;IACF;;IAEA;IACA,MAAMyF,WAAW,GAAG9G,KAAK,CAACqB,WAAW,CAACuB,GAAG,CAAC;;IAE1C;IACA,IAAIpD,KAAK,EAAEuH,QAAQ,EAAE;MACnB;MACA;MACAvH,KAAK,CAACuH,QAAQ,CAACD,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACAR,MAAM,CAACG,YAAY,CAACD,UAAU,CAACI,KAAK,CAAC,GAAG,CAAChE,GAAG,EAAEkE,WAAW,CAAC;IAC5D;EACF;EAEA;AACF", "ignoreList": []}