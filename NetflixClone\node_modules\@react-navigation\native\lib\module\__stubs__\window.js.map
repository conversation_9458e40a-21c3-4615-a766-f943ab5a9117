{"version": 3, "names": ["location", "URL", "listeners", "entries", "state", "href", "index", "currentState", "history", "pushState", "_", "path", "origin", "slice", "push", "length", "replaceState", "go", "n", "setTimeout", "Math", "abs", "entry", "for<PERSON>ach", "cb", "back", "forward", "addEventListener", "type", "listener", "removeEventListener", "filter", "window", "document", "title"], "sourceRoot": "../../../src", "sources": ["__stubs__/window.tsx"], "mappings": ";;AAAA,IAAIA,QAAQ,GAAG,IAAIC,GAAG,CAAC,EAAE,EAAE,oBAAoB,CAAC;AAEhD,IAAIC,SAAyB,GAAG,EAAE;AAClC,IAAIC,OAAO,GAAG,CAAC;EAAEC,KAAK,EAAE,IAAI;EAAEC,IAAI,EAAEL,QAAQ,CAACK;AAAK,CAAC,CAAC;AACpD,IAAIC,KAAK,GAAG,CAAC;AAEb,IAAIC,YAAiB,GAAG,IAAI;AAE5B,MAAMC,OAAO,GAAG;EACd,IAAIJ,KAAKA,CAAA,EAAG;IACV,OAAOG,YAAY;EACrB,CAAC;EAEDE,SAASA,CAACL,KAAU,EAAEM,CAAS,EAAEC,IAAY,EAAE;IAC7CX,QAAQ,GAAG,IAAIC,GAAG,CAACU,IAAI,EAAEX,QAAQ,CAACY,MAAM,CAAC;IAEzCL,YAAY,GAAGH,KAAK;IACpBD,OAAO,GAAGA,OAAO,CAACU,KAAK,CAAC,CAAC,EAAEP,KAAK,GAAG,CAAC,CAAC;IACrCH,OAAO,CAACW,IAAI,CAAC;MAAEV,KAAK;MAAEC,IAAI,EAAEL,QAAQ,CAACK;IAAK,CAAC,CAAC;IAC5CC,KAAK,GAAGH,OAAO,CAACY,MAAM,GAAG,CAAC;EAC5B,CAAC;EAEDC,YAAYA,CAACZ,KAAU,EAAEM,CAAS,EAAEC,IAAY,EAAE;IAChDX,QAAQ,GAAG,IAAIC,GAAG,CAACU,IAAI,EAAEX,QAAQ,CAACY,MAAM,CAAC;IAEzCL,YAAY,GAAGH,KAAK;IACpBD,OAAO,CAACG,KAAK,CAAC,GAAG;MAAEF,KAAK;MAAEC,IAAI,EAAEL,QAAQ,CAACK;IAAK,CAAC;EACjD,CAAC;EAEDY,EAAEA,CAACC,CAAS,EAAE;IACZC,UAAU,CAAC,MAAM;MACf,IACGD,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGf,OAAO,CAACY,MAAM,GAAGT,KAAK,IACnCY,CAAC,GAAG,CAAC,IAAIE,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC,IAAIZ,KAAM,EAC/B;QACAA,KAAK,IAAIY,CAAC;QACV,MAAMI,KAAK,GAAGnB,OAAO,CAACG,KAAK,CAAC;QAC5BN,QAAQ,GAAG,IAAIC,GAAG,CAACqB,KAAK,CAACjB,IAAI,CAAC;QAC9BE,YAAY,GAAGe,KAAK,CAAClB,KAAK;QAC1BF,SAAS,CAACqB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACjC;IACF,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAEDC,IAAIA,CAAA,EAAG;IACL,IAAI,CAACR,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EAEDS,OAAOA,CAAA,EAAG;IACR,IAAI,CAACT,EAAE,CAAC,CAAC,CAAC;EACZ;AACF,CAAC;AAED,MAAMU,gBAAgB,GAAGA,CAACC,IAAgB,EAAEC,QAAoB,KAAK;EACnE,IAAID,IAAI,KAAK,UAAU,EAAE;IACvB1B,SAAS,CAACY,IAAI,CAACe,QAAQ,CAAC;EAC1B;AACF,CAAC;AAED,MAAMC,mBAAmB,GAAGA,CAACF,IAAgB,EAAEC,QAAoB,KAAK;EACtE,IAAID,IAAI,KAAK,UAAU,EAAE;IACvB1B,SAAS,GAAGA,SAAS,CAAC6B,MAAM,CAAEP,EAAE,IAAKA,EAAE,KAAKK,QAAQ,CAAC;EACvD;AACF,CAAC;AAED,OAAO,MAAMG,MAAM,GAAG;EACpBC,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAG,CAAC;EACvB,IAAIlC,QAAQA,CAAA,EAAG;IACb,OAAOA,QAAQ;EACjB,CAAC;EACDQ,OAAO;EACPmB,gBAAgB;EAChBG,mBAAmB;EACnB,IAAIE,MAAMA,CAAA,EAAG;IACX,OAAOA,MAAM;EACf;AACF,CAAC", "ignoreList": []}