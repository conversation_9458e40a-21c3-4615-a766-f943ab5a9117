import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import { NETFLIX_COLORS } from './src/utils/constants';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import SearchScreen from './src/screens/SearchScreen';
import MediaDetailScreen from './src/screens/MediaDetailScreen';
import TVEpisodesScreen from './src/screens/TVEpisodesScreen';
import PlayerScreen from './src/screens/PlayerScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar
        style="light"
        backgroundColor={NETFLIX_COLORS.black}
        translucent={Platform.OS === 'android'}
      />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: NETFLIX_COLORS.black },
          animationEnabled: true,
          gestureEnabled: true,
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="Search"
          component={SearchScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="MediaDetail"
          component={MediaDetailScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="TVEpisodes"
          component={TVEpisodesScreen}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
        <Stack.Screen
          name="Player"
          component={PlayerScreen}
          options={{
            animationTypeForReplace: 'push',
            gestureEnabled: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
