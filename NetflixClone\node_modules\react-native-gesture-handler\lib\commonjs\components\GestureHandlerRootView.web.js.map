{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_GestureHandlerRootViewContext", "_interopRequireDefault", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "GestureHandlerRootView", "style", "rest", "jsx", "Provider", "value", "children", "View", "styles", "container", "StyleSheet", "create", "flex"], "sourceRoot": "../../../src", "sources": ["components/GestureHandlerRootView.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,8BAAA,GAAAC,sBAAA,CAAAH,OAAA;AAA6E,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAM,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAK9D,SAASgB,sBAAsBA,CAAC;EAC7CC,KAAK;EACL,GAAGC;AACwB,CAAC,EAAE;EAC9B,oBACE,IAAAtB,WAAA,CAAAuB,GAAA,EAACzB,8BAAA,CAAAK,OAA6B,CAACqB,QAAQ;IAACC,KAAK;IAAAC,QAAA,eAC3C,IAAA1B,WAAA,CAAAuB,GAAA,EAAC1B,YAAA,CAAA8B,IAAI;MAACN,KAAK,EAAEA,KAAK,IAAIO,MAAM,CAACC,SAAU;MAAA,GAAKP;IAAI,CAAG;EAAC,CACd,CAAC;AAE7C;AAEA,MAAMM,MAAM,GAAGE,uBAAU,CAACC,MAAM,CAAC;EAC/BF,SAAS,EAAE;IAAEG,IAAI,EAAE;EAAE;AACvB,CAAC,CAAC", "ignoreList": []}