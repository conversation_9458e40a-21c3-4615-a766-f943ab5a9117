{"version": 3, "names": ["PressableStateMachine", "constructor", "steps", "states", "currentStepIndex", "eventPayload", "reset", "handleEvent", "eventName", "step", "callback", "length"], "sourceRoot": "../../../../src", "sources": ["components/Pressable/StateMachine.tsx"], "mappings": ";;AAOA,MAAMA,qBAAqB,CAAC;EAK1BC,WAAWA,CAACC,KAAwB,EAAE;IACpC,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,YAAY,GAAG,IAAI;EAC1B;EAEOC,KAAKA,CAAA,EAAG;IACb,IAAI,CAACF,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,YAAY,GAAG,IAAI;EAC1B;EAEOE,WAAWA,CAACC,SAAiB,EAAEH,YAA6B,EAAE;IACnE,MAAMI,IAAI,GAAG,IAAI,CAACN,MAAM,CAAC,IAAI,CAACC,gBAAgB,CAAC;IAC/C,IAAI,CAACC,YAAY,GAAGA,YAAY,IAAI,IAAI,CAACA,YAAY;IAErD,IAAII,IAAI,CAACD,SAAS,KAAKA,SAAS,EAAE;MAChC,IAAI,IAAI,CAACJ,gBAAgB,GAAG,CAAC,EAAE;QAC7B;QACA,IAAI,CAACE,KAAK,CAAC,CAAC;QACZ,IAAI,CAACC,WAAW,CAACC,SAAS,EAAEH,YAAY,CAAC;MAC3C;MACA;IACF;IAEA,IAAI,IAAI,CAACA,YAAY,IAAII,IAAI,CAACC,QAAQ,EAAE;MACtCD,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACL,YAAY,CAAC;IAClC;IAEA,IAAI,CAACD,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACA,gBAAgB,KAAK,IAAI,CAACD,MAAM,CAACQ,MAAM,EAAE;MAChD,IAAI,CAACL,KAAK,CAAC,CAAC;IACd;EACF;AACF;AAEA,SAASN,qBAAqB", "ignoreList": []}