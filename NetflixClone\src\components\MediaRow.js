import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { netflixStyles } from '../styles/netflix';
import { NETFLIX_COLORS } from '../utils/constants';
import MediaCard from './MediaCard';

const MediaRow = memo(({
  title,
  data,
  onItemPress,
  loading = false,
  size = 'normal',
  showProgress = false,
  getProgress = null
}) => {
  const renderItem = useCallback(({ item, index }) => {
    const progress = showProgress && getProgress ? getProgress(item) : 0;

    return (
      <MediaCard
        item={item}
        onPress={onItemPress}
        size={size}
        showProgress={showProgress}
        progress={progress}
      />
    );
  }, [onItemPress, size, showProgress, getProgress]);

  const keyExtractor = useCallback((item, index) => `${item.id}-${index}`, []);

  if (loading) {
    return (
      <View style={netflixStyles.mediaRow}>
        <Text style={netflixStyles.mediaRowTitle}>{title}</Text>
        <View style={{
          height: size === 'large' ? 240 : 180,
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <ActivityIndicator size="large" color={NETFLIX_COLORS.primary} />
        </View>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <View style={netflixStyles.mediaRow}>
      <Text style={netflixStyles.mediaRowTitle}>{title}</Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={netflixStyles.mediaRowScroll}
        decelerationRate="fast"
        snapToInterval={size === 'large' ? 170 : 130}
        snapToAlignment="start"
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        getItemLayout={(data, index) => ({
          length: size === 'large' ? 170 : 130,
          offset: (size === 'large' ? 170 : 130) * index,
          index,
        })}
      />
    </View>
  );
});

export default MediaRow;
